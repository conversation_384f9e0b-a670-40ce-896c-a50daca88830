import json
import os
import faiss
import numpy as np
from tqdm import tqdm
from openai import OpenAI
import dotenv

dotenv.load_dotenv()

# === CONFIGURATION ===
EMBEDDING_MODEL = "text-embedding-3-small"
CHUNK_FILE = "onenote_chunks.json"
INDEX_FILE = "faiss.index"
METADATA_FILE = "faiss_metadata.json"

# === OPENAI SETUP ===
client = OpenAI(api_key="********************************************************************************************************************************************************************")

# === LOAD CHUNKS ===
with open(CHUNK_FILE, "r", encoding="utf-8") as f:
    chunks = json.load(f)

print(f"📦 Loaded {len(chunks)} chunks from {CHUNK_FILE}")

# === EMBED & INDEX ===
embeddings = []
metadata = []

for chunk in tqdm(chunks, desc="🔁 Embedding"):
    try:
        text = chunk["text"].replace("\n", " ").strip()
        if not text:
            continue

        response = client.embeddings.create(
            input=text,
            model=EMBEDDING_MODEL
        )
        embedding = response.data[0].embedding
        embeddings.append(embedding)

        # Preserve full metadata (for traceability)
        metadata.append(chunk["metadata"])

    except Exception as e:
        print("❌ Error embedding chunk:", e)

# === SAFETY CHECK ===
if not embeddings:
    raise RuntimeError("❌ No embeddings created. Check OpenAI credentials or input format.")

# === FAISS SETUP ===
np_embeddings = np.array(embeddings).astype("float32")
dimension = len(np_embeddings[0])

index = faiss.IndexFlatL2(dimension)
index.add(np_embeddings)

print(f"✅ FAISS index created with {index.ntotal} vectors")

# === SAVE ARTIFACTS ===
faiss.write_index(index, INDEX_FILE)
print(f"💾 FAISS index saved to {INDEX_FILE}")

with open(METADATA_FILE, "w", encoding="utf-8") as f:
    json.dump(metadata, f, indent=2, ensure_ascii=False)
print(f"💾 Metadata saved to {METADATA_FILE}")