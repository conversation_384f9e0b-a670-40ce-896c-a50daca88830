import json
import os
import faiss
import numpy as np
from tqdm import tqdm
from openai import OpenAI
from typing import List, Dict, Tuple, Optional

# === CONFIGURATION ===
EMBEDDING_MODEL = "text-embedding-3-small"
CHUNK_FILE = "/tmp/onenote_chunks.json"  # Use /tmp for Lambda
INDEX_FILE = "/tmp/faiss.index"  # Use /tmp for Lambda
METADATA_FILE = "/tmp/faiss_metadata.json"  # Use /tmp for Lambda

class EmbeddingGenerator:
    def __init__(self, openai_api_key: Optional[str] = None):
        self.client = None
        if openai_api_key:
            self.client = OpenAI(api_key=openai_api_key)
        self.index = None
        self.metadata = []
        
    def load_chunks(self, chunk_file: str = CHUNK_FILE) -> List[Dict]:
        """Load chunks from JSON file"""
        if not os.path.exists(chunk_file):
            print(f"📂 No chunks file found at {chunk_file}")
            return []
        with open(chunk_file, "r", encoding="utf-8") as f:
            chunks = json.load(f)
        print(f"📦 Loaded {len(chunks)} chunks from {chunk_file}")
        return chunks

    def load_existing_index(self, index_file: str = INDEX_FILE, metadata_file: str = METADATA_FILE) -> bool:
        """Load existing FAISS index and metadata if available"""
        try:
            if os.path.exists(index_file) and os.path.exists(metadata_file):
                self.index = faiss.read_index(index_file)
                with open(metadata_file, "r", encoding="utf-8") as f:
                    self.metadata = json.load(f)
                print(f"📂 Loaded existing index with {self.index.ntotal} vectors")
                return True
            else:
                print("📂 No existing index found, will create new one")
                return False
        except Exception as e:
            print(f"⚠️ Error loading existing index: {e}")
            return False

    def create_embeddings(self, chunks: List[Dict]) -> Tuple[List[List[float]], List[Dict]]:
        """Create embeddings for chunks and return embeddings + metadata"""
        if not self.client:
            raise RuntimeError("❌ OpenAI client not initialized. Please provide OPENAI_API_KEY.")
            
        embeddings = []
        metadata = []

        for chunk in tqdm(chunks, desc="🔁 Embedding"):
            try:
                text = chunk["text"].replace("\n", " ").strip()
                if not text:
                    continue

                response = self.client.embeddings.create(
                    input=text,
                    model=EMBEDDING_MODEL
                )
                embedding = response.data[0].embedding
                embeddings.append(embedding)

                # Preserve full metadata (for traceability)
                metadata.append(chunk["metadata"])

            except Exception as e:
                print(f"❌ Error embedding chunk: {e}")
                continue

        # === SAFETY CHECK ===
        if not embeddings:
            raise RuntimeError("❌ No embeddings created. Check OpenAI credentials or input format.")

        return embeddings, metadata

    def create_new_index(self, chunks: List[Dict]) -> None:
        """Create a new FAISS index from chunks (always rebuilds from scratch)"""
        print("🆕 Creating new FAISS index from scratch...")
        
        embeddings, metadata = self.create_embeddings(chunks)
        
        # === FAISS SETUP ===
        np_embeddings = np.array(embeddings).astype("float32")
        dimension = len(np_embeddings[0])

        self.index = faiss.IndexFlatL2(dimension)
        self.index.add(np_embeddings)
        self.metadata = metadata

        print(f"✅ FAISS index created with {self.index.ntotal} vectors")

    def save_index(self, index_file: str = INDEX_FILE, metadata_file: str = METADATA_FILE) -> None:
        """Save FAISS index and metadata to files"""
        if not self.index:
            raise RuntimeError("❌ No index to save")
            
        faiss.write_index(self.index, index_file)
        print(f"💾 FAISS index saved to {index_file}")

        with open(metadata_file, "w", encoding="utf-8") as f:
            json.dump(self.metadata, f, indent=2, ensure_ascii=False)
        print(f"💾 Metadata saved to {metadata_file}")

    def get_index_info(self) -> Dict:
        """Get information about the current index"""
        if not self.index:
            return {"error": "No index loaded"}
            
        return {
            "total_vectors": self.index.ntotal,
            "dimension": self.index.d,
            "metadata_count": len(self.metadata)
        }

# === MAIN EXECUTION (for testing) ===
if __name__ == "__main__":
    generator = EmbeddingGenerator()
    chunks = generator.load_chunks()
    generator.create_new_index(chunks)
    generator.save_index()
    print(f"Index info: {generator.get_index_info()}") 