import os
import boto3
import json
from datetime import datetime
from typing import Dict, Optional
from dotenv import load_dotenv

load_dotenv()

S3_BUCKET = os.getenv("S3_BUCKET_NAME")
S3_PREFIX = "rag-knowledge"  # can be changed if needed

# Initialize S3 client using default credentials (Lambda provides these automatically)
s3 = boto3.client("s3")

def get_today_prefix() -> str:
    today = datetime.now().strftime("%Y-%m-%d")
    return f"{S3_PREFIX}/{today}/"

def get_latest_files() -> Optional[Dict]:
    """Get the latest index and metadata file paths from S3"""
    try:
        latest_key = f"{S3_PREFIX}/latest.json"
        response = s3.get_object(Bucket=S3_BUCKET, Key=latest_key)
        latest_data = json.loads(response['Body'].read().decode('utf-8'))
        print(f"📂 Found latest files: {latest_data}")
        return latest_data
    except Exception as e:
        print(f"⚠️ No latest.json found or error reading: {e}")
        return None

def download_file(s3_key: str, local_path: str) -> bool:
    """Download file from S3 to local path"""
    try:
        print(f"⬇️ Downloading s3://{S3_BUCKET}/{s3_key} to {local_path}")
        s3.download_file(S3_BUCKET, s3_key, local_path)
        return True
    except Exception as e:
        print(f"❌ Error downloading {s3_key}: {e}")
        return False

def download_msal_cache_files() -> Dict[str, bool]:
    """Download MSAL cache files from S3 to /tmp directory"""
    print("🔐 Downloading MSAL cache files from S3...")
    
    # Define cache files and their S3 keys
    cache_files = {
        "msal_cache.json": f"{S3_PREFIX}/auth/msal_cache.json",
        "msal_token_cache.bin": f"{S3_PREFIX}/auth/msal_token_cache.bin"
    }
    
    download_results = {}
    
    for filename, s3_key in cache_files.items():
        local_path = f"/tmp/{filename}"
        success = download_file(s3_key, local_path)
        download_results[filename] = success
        
        if success:
            print(f"✅ Downloaded {filename}")
        else:
            print(f"⚠️ Failed to download {filename} (may not exist yet)")
    
    return download_results

def upload_msal_cache_files() -> Dict[str, bool]:
    """Upload MSAL cache files from /tmp to S3"""
    print("🔐 Uploading MSAL cache files to S3...")
    
    # Define cache files and their S3 keys
    cache_files = {
        "msal_cache.json": f"{S3_PREFIX}/auth/msal_cache.json",
        "msal_token_cache.bin": f"{S3_PREFIX}/auth/msal_token_cache.bin"
    }
    
    upload_results = {}
    
    for filename, s3_key in cache_files.items():
        local_path = f"/tmp/{filename}"
        
        if os.path.exists(local_path):
            try:
                upload_file(local_path, s3_key)
                upload_results[filename] = True
                print(f"✅ Uploaded {filename}")
            except Exception as e:
                print(f"❌ Failed to upload {filename}: {e}")
                upload_results[filename] = False
        else:
            print(f"⚠️ {filename} not found at {local_path}")
            upload_results[filename] = False
    
    return upload_results

def download_latest_files() -> Optional[Dict]:
    """Download the latest index and metadata files from S3 to /tmp"""
    latest_files = get_latest_files()
    if not latest_files:
        return None
    
    # Download to /tmp directory (Lambda writable)
    tmp_index_path = "/tmp/faiss.index"
    tmp_metadata_path = "/tmp/faiss_metadata.json"
    tmp_chunks_path = "/tmp/onenote_chunks.json"
    
    success = True
    success &= download_file(latest_files["latest_index_path"], tmp_index_path)
    success &= download_file(latest_files["latest_metadata_path"], tmp_metadata_path)
    
    # Try to download chunks file if it exists
    if "latest_chunks_path" in latest_files:
        chunks_downloaded = download_file(latest_files["latest_chunks_path"], tmp_chunks_path)
        if not chunks_downloaded:
            print("⚠️ No chunks file found in S3, will create new one")
    else:
        print("⚠️ No chunks file path in latest.json, will create new one")
        chunks_downloaded = False
    
    # Download MSAL cache files
    print("🔐 Downloading MSAL authentication cache...")
    msal_downloads = download_msal_cache_files()
    
    if success:
        return {
            "index_path": tmp_index_path,
            "metadata_path": tmp_metadata_path,
            "chunks_path": tmp_chunks_path,
            "chunks_downloaded": chunks_downloaded,
            "msal_cache_downloaded": msal_downloads
        }
    return None

def upload_file(local_path: str, s3_key: str):
    print(f"⬆️ Uploading {local_path} to s3://{S3_BUCKET}/{s3_key}")
    s3.upload_file(local_path, S3_BUCKET, s3_key)

def upload_artifacts(index_path: str, metadata_path: str) -> Dict:
    """Upload FAISS index and metadata to S3, return their S3 keys"""
    prefix = get_today_prefix()
    index_s3_key = f"{prefix}faiss.index"
    metadata_s3_key = f"{prefix}faiss_metadata.json"
    upload_file(index_path, index_s3_key)
    upload_file(metadata_path, metadata_s3_key)
    return {
        "index_s3_key": index_s3_key,
        "metadata_s3_key": metadata_s3_key
    }

def upload_chunks_file(chunks_path: str) -> str:
    """Upload the chunks file to S3 in the daily folder"""
    prefix = get_today_prefix()
    chunks_s3_key = f"{prefix}onenote_chunks.json"
    upload_file(chunks_path, chunks_s3_key)
    print(f"📦 Uploaded chunks file to s3://{S3_BUCKET}/{chunks_s3_key}")
    return chunks_s3_key

def update_latest_json(index_s3_key: str, metadata_s3_key: str, chunks_s3_key: str):
    """Update the latest.json pointer in the S3 bucket root"""
    latest = {
        "latest_index_path": index_s3_key,
        "latest_metadata_path": metadata_s3_key,
        "latest_chunks_path": chunks_s3_key
    }
    latest_json = json.dumps(latest, indent=2)
    s3.put_object(Bucket=S3_BUCKET, Key=f"{S3_PREFIX}/latest.json", Body=latest_json, ContentType="application/json")
    print(f"📝 Updated latest.json at s3://{S3_BUCKET}/{S3_PREFIX}/latest.json")

def finalize_and_upload_cache():
    """Upload updated MSAL cache files back to S3 at the end of execution"""
    print("🔄 Finalizing MSAL cache upload...")
    return upload_msal_cache_files() 