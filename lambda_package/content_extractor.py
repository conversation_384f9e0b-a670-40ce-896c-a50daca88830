import os
import re
import requests
from bs4 import BeautifulSoup
import json
import msal
from typing import List, Dict, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Microsoft Graph API Configuration - Same as working file
client_id = "f1547c8c-ac20-429e-bb35-bf70461505d6"
tenant_id = "25c282df-1bd0-4ae9-915e-ea3003bdd835"
authority = f"https://login.microsoftonline.com/{tenant_id}"
scopes = ["Sites.Read.All", "Notes.Read.All"]
SITE_ID = "creospaninc.sharepoint.com,e403106b-31d0-4181-b487-2ef10c039907,c3316d29-5ff0-4cbb-965a-a57f407cd771"

CHUNK_SIZE = 1000
CHUNK_OVERLAP = 200

class ContentExtractor:
    def __init__(self):
        self.headers = None
        
    def authenticate(self) -> str:
        """Authenticate with Microsoft Graph API and return access token - Same as working file"""
        # === TOKEN CACHE ===
        cache_file = "msal_cache.json"
        token_cache = msal.SerializableTokenCache()
        if os.path.exists(cache_file):
            token_cache.deserialize(open(cache_file, "r").read())

        app = msal.PublicClientApplication(client_id=client_id, authority=authority, token_cache=token_cache)
        accounts = app.get_accounts()
        if accounts:
            result = app.acquire_token_silent(scopes, account=accounts[0])
        else:
            result = app.acquire_token_interactive(scopes)

        with open(cache_file, "w") as f:
            f.write(token_cache.serialize())

        if "access_token" not in result:
            print("❌ Auth failed")
            raise Exception("Authentication failed")

        self.headers = {"Authorization": f"Bearer {result['access_token']}"}
        return result['access_token']

    def get_notebooks(self) -> List[Dict]:
        """Get all notebooks from the site"""
        if not self.headers:
            self.authenticate()
            
        notebooks_url = f"https://graph.microsoft.com/v1.0/sites/{SITE_ID}/onenote/notebooks"
        notebooks_resp = requests.get(notebooks_url, headers=self.headers)
        notebooks = notebooks_resp.json().get("value", []) if notebooks_resp.ok else []
        
        if not notebooks:
            print("❌ No notebooks found.")
            return []
            
        return notebooks

    def get_all_sections_from_group(self, group_id: str, group_name: str, headers: Dict) -> List[Dict]:
        """Recursively get all sections from a section group and its nested groups"""
        sections = []
        
        try:
            # Get sections in this group
            group_sections_url = f"https://graph.microsoft.com/v1.0/sites/{SITE_ID}/onenote/sectionGroups/{group_id}/sections"
            group_sections_resp = requests.get(group_sections_url, headers=headers)
            
            if group_sections_resp.ok:
                group_sections = group_sections_resp.json().get("value", [])
                for s in group_sections:
                    s["group"] = group_name
                    s["group_id"] = group_id  # Keep for debugging/tracking
                sections.extend(group_sections)
            
            # Get nested section groups and recurse
            nested_groups_url = f"https://graph.microsoft.com/v1.0/sites/{SITE_ID}/onenote/sectionGroups/{group_id}/sectionGroups"
            nested_groups_resp = requests.get(nested_groups_url, headers=headers)
            
            if nested_groups_resp.ok:
                nested_groups = nested_groups_resp.json().get("value", [])
                for nested_group in nested_groups:
                    # Recursively get sections from nested group
                    nested_sections = self.get_all_sections_from_group(
                        nested_group["id"], 
                        nested_group["displayName"], 
                        headers
                    )
                    sections.extend(nested_sections)
                    
        except requests.RequestException as e:
            print(f"⚠️ Error fetching sections from group {group_name}: {e}")
        
        return sections

    def get_sections(self, notebook_id: str) -> List[Dict]:
        """Get all sections in a notebook, including from nested section groups."""
        if not self.headers:
            self.authenticate()
            
        all_sections = []

        # 1. Top-level sections
        sections_url = f"https://graph.microsoft.com/v1.0/sites/{SITE_ID}/onenote/notebooks/{notebook_id}/sections"
        sections_resp = requests.get(sections_url, headers=self.headers)
        if sections_resp.ok:
            all_sections.extend(sections_resp.json().get("value", []))

        # 2. Section group sections (recursive with improved function)
        sg_url = f"https://graph.microsoft.com/v1.0/sites/{SITE_ID}/onenote/notebooks/{notebook_id}/sectionGroups"
        sg_resp = requests.get(sg_url, headers=self.headers)
        
        if sg_resp.ok:
        for group in sg_resp.json().get("value", []):
                group_sections = self.get_all_sections_from_group(
                    group["id"], 
                    group["displayName"], 
                    self.headers
                )
            all_sections.extend(group_sections)

        return all_sections

    def get_page_content(self, page_id: str) -> Optional[str]:
        """Extract text content from a OneNote page"""
        content_url = f"https://graph.microsoft.com/v1.0/sites/{SITE_ID}/onenote/pages/{page_id}/content"
        content_resp = requests.get(content_url, headers=self.headers)

        if not content_resp.ok:
            print(f"❌ Could not fetch content for page {page_id}")
            return None

        soup = BeautifulSoup(content_resp.text, "html.parser")
        text = soup.get_text().strip()
        
        if not text:
            print(f"⚠️ Empty page {page_id}")
            return None
            
        return text

    def chunk_text(self, text: str, page_metadata: Dict) -> List[Dict]:
        """Chunk text into smaller pieces with metadata"""
        chunks = []
        start = 0
        index = 0
        
        while start < len(text):
            end = start + CHUNK_SIZE
            chunk_text = text[start:end].strip()
            
            chunk = {
                "text": chunk_text,
                "metadata": {
                    **page_metadata,
                    "chunk_index": index
                }
            }
            
            chunks.append(chunk)
            start += CHUNK_SIZE - CHUNK_OVERLAP
            index += 1
            
        return chunks

    def extract_pages_content(self, pages: List[Dict], notebook_name: str, section_name: str) -> List[Dict]:
        """Extract content from specific pages"""
        all_chunks = []
        
        for page in pages:
            print(f"  📄 Page: {page['title']}")
            
            # Fetch full page metadata to ensure we have links structure
            page_url = f"https://graph.microsoft.com/v1.0/sites/{SITE_ID}/onenote/pages/{page['id']}"
            page_resp = requests.get(page_url, headers=self.headers)
            if page_resp.ok:
                full_page = page_resp.json()
                # Update page object with full metadata
                page.update(full_page)
            
            content = self.get_page_content(page['id'])
            if not content:
                continue

            # Create page metadata
            page_metadata = {
                "notebook": notebook_name,
                "section": section_name,
                "page": page["title"],
                "page_url": page.get("links", {}).get("oneNoteWebUrl", {}).get("href"),
                "page_id": page["id"],
                "last_modified": page.get("lastModifiedDateTime")
            }

            # Chunk the content
            chunks = self.chunk_text(content, page_metadata)
            all_chunks.extend(chunks)
            
            print(f"     🔹 {len(chunks)} chunks created")

        return all_chunks

    def extract_all_content(self) -> List[Dict]:
        """Extract content from all notebooks and sections"""
        if not self.headers:
            self.authenticate()
            
        notebooks = self.get_notebooks()
        all_chunks = []

        for nb in notebooks:
            print(f"\n📓 Notebook: {nb['displayName']}")
            sections = self.get_sections(nb['id'])

            for section in sections:
                label = f"{section['displayName']}"
                if "group" in section:
                    label += f" (Group: {section['group']})"
                print(f"\n📂 Section: {label}")

                # Get all pages in this section
                pages_url = f"https://graph.microsoft.com/v1.0/sites/{SITE_ID}/onenote/sections/{section['id']}/pages"
                pages_resp = requests.get(pages_url, headers=self.headers)
                pages = pages_resp.json().get("value", []) if pages_resp.ok else []

                # Extract content from all pages
                section_chunks = self.extract_pages_content(pages, nb["displayName"], section["displayName"])
                all_chunks.extend(section_chunks)

        return all_chunks

    def extract_selective_content(self, updated_pages: List[Dict]) -> List[Dict]:
        """Extract content only from updated pages"""
        if not self.headers:
            self.authenticate()
            
        all_chunks = []
        
        # Group pages by section for better organization
        pages_by_section = {}
        for page in updated_pages:
            section_id = page['section_id']
            if section_id not in pages_by_section:
                pages_by_section[section_id] = {
                    'section_name': page['section_name'],
                    'pages': []
                }
            pages_by_section[section_id]['pages'].append(page)

        for section_id, section_data in pages_by_section.items():
            print(f"\n📂 Section: {section_data['section_name']}")
            
            # Convert to expected format for extract_pages_content
            pages_formatted = []
            for page in section_data['pages']:
                # Handle both 'id' (from change detector) and 'page_id' (legacy) fields
                page_id = page.get('id') or page.get('page_id')
                if not page_id:
                    print(f"⚠️ Skipping page with missing ID: {page.get('title', 'Unknown')}")
                    continue
                    
                pages_formatted.append({
                    'id': page_id,
                    'title': page.get('title', 'Unknown'),
                    'lastModifiedDateTime': page.get('lastModifiedDateTime')
                })
            
            # Extract content from updated pages
            if pages_formatted:  # Only process if we have valid pages
                section_chunks = self.extract_pages_content(
                    pages_formatted, 
                    page.get('notebook_name', 'Unknown'),  # Use the last page's notebook name
                    section_data['section_name']
                )
                all_chunks.extend(section_chunks)

        return all_chunks

    def save_chunks_to_json(self, chunks: List[Dict], filename: str = "onenote_chunks.json"):
        """Save extracted chunks to JSON file"""
        chunks_file_path = f"/tmp/{filename}" if os.getenv('AWS_LAMBDA_FUNCTION_NAME') else filename
        
        with open(chunks_file_path, "w", encoding="utf-8") as f:
            json.dump(chunks, f, ensure_ascii=False, indent=2)
        
        print(f"💾 Saved {len(chunks)} chunks to {chunks_file_path}")
        return chunks_file_path

    def finalize(self):
        """Finalize content extraction - upload any cache changes to S3"""
        # No specific cache management needed here as msal handles its own cache
        pass

# === MAIN EXECUTION (for testing) ===
if __name__ == "__main__":
    extractor = ContentExtractor()
    chunks = extractor.extract_all_content()
    extractor.save_chunks_to_json(chunks) 