
# This file was generated by 'versioneer.py' (0.26) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2023-04-22T13:47:13-0400",
 "dirty": false,
 "error": null,
 "full-revisionid": "14bb214bca49b167abc375fa873466a811e62102",
 "version": "1.24.3"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
