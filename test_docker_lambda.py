#!/usr/bin/env python3
"""
Test script to invoke Docker Lambda container
"""

import json
import requests
import time
import subprocess
import os
import dotenv

dotenv.load_dotenv()

def test_docker_lambda():
    """Test the Docker Lambda container"""
    print("🐳 Testing Docker Lambda Container")
    print("=" * 50)
    
    # Test event
    test_event = {
        "days_ago": 1
    }
    
    # Start Docker container in background
    print("🚀 Starting Docker container...")
    docker_cmd = [
        "docker", "run", "--rm", "-p", "9000:8080",
        "-e", "AWS_LAMBDA_FUNCTION_NAME=test",
        "-e", f"S3_BUCKET_NAME={os.getenv('S3_BUCKET_NAME', 'knowledgebot-faiss-embedding')}",
        "-e", f"OPENAI_API_KEY={os.getenv('OPENAI_API_KEY')}",
        "-e", f"AWS_ACCESS_KEY_ID={os.getenv('AWS_ACCESS_KEY_ID')}",
        "-e", f"AWS_SECRET_ACCESS_KEY={os.getenv('AWS_SECRET_ACCESS_KEY')}",
        "-e", f"AWS_DEFAULT_REGION={os.getenv('AWS_DEFAULT_REGION', 'us-east-1')}",
        "knowledge-pipeline:latest"
    ]
    
    # Start container
    container = subprocess.Popen(docker_cmd)
    
    try:
        # Wait for container to be ready
        print("⏳ Waiting for container to start...")
        time.sleep(5)
        
        # Invoke the function
        print("📞 Invoking Lambda function...")
        url = "http://localhost:9000/2015-03-31/functions/function/invocations"
        
        response = requests.post(
            url,
            json=test_event,
            timeout=300  # 5 minute timeout
        )
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Lambda invocation successful!")
            print(f"📋 Result: {json.dumps(result, indent=2)}")
        else:
            print(f"❌ Lambda invocation failed: {response.text}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        # Stop container
        print("🛑 Stopping container...")
        container.terminate()
        container.wait()

if __name__ == "__main__":
    test_docker_lambda() 