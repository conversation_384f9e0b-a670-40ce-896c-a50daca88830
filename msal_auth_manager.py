import msal
import os
import json
from typing import Optional
from s3_manager import download_msal_cache_files, upload_msal_cache_files

class MSALAuthManager:
    """
    MSAL Authentication Manager with S3 cache support
    Handles authentication for both local development and Lambda/Docker environments
    """
    
    def __init__(self, client_id: str, tenant_id: str, scopes: list):
        self.client_id = client_id
        self.tenant_id = tenant_id
        self.authority = f"https://login.microsoftonline.com/{tenant_id}"
        self.scopes = scopes
        self.access_token = None
        self.app = None
        
        # Determine cache file paths based on environment
        self.is_lambda = os.getenv('AWS_LAMBDA_FUNCTION_NAME') is not None
        self.cache_dir = "/tmp" if self.is_lambda else "."
        
        self.json_cache_path = f"{self.cache_dir}/msal_cache.json"
        self.bin_cache_path = f"{self.cache_dir}/msal_token_cache.bin"
        
        print(f"🔐 MSAL Auth Manager initialized")
        print(f"   Environment: {'Lambda' if self.is_lambda else 'Local'}")
        print(f"   Cache directory: {self.cache_dir}")
    
    def _load_cache_from_s3(self):
        """Download MSAL cache files from S3 if in Lambda environment"""
        if self.is_lambda:
            print("📥 Loading MSAL cache from S3...")
            download_results = download_msal_cache_files()
            
            for filename, success in download_results.items():
                if success:
                    print(f"✅ Successfully loaded {filename} from S3")
                else:
                    print(f"⚠️ Could not load {filename} from S3 (may not exist yet)")
        else:
            print("🏠 Using local MSAL cache files")
    
    def _save_cache_to_s3(self):
        """Upload MSAL cache files to S3 if in Lambda environment"""
        if self.is_lambda:
            print("📤 Saving MSAL cache to S3...")
            upload_results = upload_msal_cache_files()
            
            for filename, success in upload_results.items():
                if success:
                    print(f"✅ Successfully saved {filename} to S3")
                else:
                    print(f"❌ Failed to save {filename} to S3")
        else:
            print("🏠 MSAL cache saved locally")
    
    def _initialize_token_cache(self) -> msal.SerializableTokenCache:
        """Initialize and load the MSAL token cache"""
        token_cache = msal.SerializableTokenCache()
        
        # Try to load from JSON cache first
        if os.path.exists(self.json_cache_path):
            try:
                with open(self.json_cache_path, "r") as f:
                    cache_data = f.read()
                    if cache_data.strip():  # Check if file is not empty
                        token_cache.deserialize(cache_data)
                        print(f"📂 Loaded token cache from {self.json_cache_path}")
                    else:
                        print(f"⚠️ Cache file {self.json_cache_path} is empty")
            except Exception as e:
                print(f"⚠️ Error loading cache from {self.json_cache_path}: {e}")
        else:
            print(f"⚠️ No cache file found at {self.json_cache_path}")
        
        return token_cache
    
    def _save_token_cache(self, token_cache: msal.SerializableTokenCache):
        """Save the token cache to both JSON and binary formats"""
        try:
            # Save JSON format
            cache_data = token_cache.serialize()
            if cache_data:  # Only save if there's actual data
                with open(self.json_cache_path, "w") as f:
                    f.write(cache_data)
                print(f"💾 Saved token cache to {self.json_cache_path}")
                
                # Also save as binary for compatibility
                with open(self.bin_cache_path, "w") as f:
                    f.write(cache_data)
                print(f"💾 Saved token cache to {self.bin_cache_path}")
            else:
                print("⚠️ No cache data to save")
                
        except Exception as e:
            print(f"❌ Error saving token cache: {e}")
    
    def authenticate(self) -> str:
        """
        Authenticate with Microsoft Graph API and return access token
        Handles both interactive (local) and silent (Lambda) authentication
        """
        print("🔐 Starting MSAL authentication...")
        
        # Load cache from S3 if in Lambda
        self._load_cache_from_s3()
        
        # Initialize token cache
        token_cache = self._initialize_token_cache()
        
        # Create MSAL app
        self.app = msal.PublicClientApplication(
            client_id=self.client_id,
            authority=self.authority,
            token_cache=token_cache
        )
        
        # Try silent authentication first
        accounts = self.app.get_accounts()
        result = None
        
        if accounts:
            print(f"🔍 Found {len(accounts)} cached account(s)")
            result = self.app.acquire_token_silent(self.scopes, account=accounts[0])
            
        if result and "access_token" in result:
            print("✅ Silent authentication successful")
            self.access_token = result['access_token']
        else:
            if self.is_lambda:
                # In Lambda, we can't do interactive auth, so this is an error
                error_msg = "❌ Silent authentication failed in Lambda environment"
                if result and "error" in result:
                    error_msg += f": {result.get('error_description', result['error'])}"
                raise RuntimeError(error_msg)
            else:
                # Local environment - try interactive authentication
                print("🖥️ Attempting interactive authentication...")
                result = self.app.acquire_token_interactive(self.scopes)
                
                if result and "access_token" in result:
                    print("✅ Interactive authentication successful")
                    self.access_token = result['access_token']
                else:
                    error_msg = "❌ Interactive authentication failed"
                    if result and "error" in result:
                        error_msg += f": {result.get('error_description', result['error'])}"
                    raise RuntimeError(error_msg)
        
        # Save updated cache
        self._save_token_cache(token_cache)
        
        # Upload cache to S3 if in Lambda
        self._save_cache_to_s3()
        
        return self.access_token
    
    def get_auth_headers(self) -> dict:
        """Get authentication headers for API requests"""
        if not self.access_token:
            self.authenticate()
        return {"Authorization": f"Bearer {self.access_token}"}
    
    def finalize(self):
        """Finalize authentication - upload any cache changes to S3"""
        print("🔄 Finalizing MSAL authentication...")
        self._save_cache_to_s3()

# Convenience function for easy import
def create_auth_manager(client_id: str, tenant_id: str, scopes: list) -> MSALAuthManager:
    """Create and return an MSAL Auth Manager"""
    return MSALAuthManager(client_id, tenant_id, scopes) 