import msal
import os
import requests
from bs4 import BeautifulSoup
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Microsoft Graph API Configuration
client_id = "f1547c8c-ac20-429e-bb35-bf70461505d6"
tenant_id = "25c282df-1bd0-4ae9-915e-ea3003bdd835"
authority = f"https://login.microsoftonline.com/{tenant_id}"
scopes = ["Sites.Read.All", "Notes.Read.All"]
SITE_ID = "creospaninc.sharepoint.com,e403106b-31d0-4181-b487-2ef10c039907,c3316d29-5ff0-4cbb-965a-a57f407cd771"

CHUNK_SIZE = 1000
CHUNK_OVERLAP = 200

# === TOKEN CACHE ===
cache_file = "msal_cache.json"
token_cache = msal.SerializableTokenCache()
if os.path.exists(cache_file):
    token_cache.deserialize(open(cache_file, "r").read())

app = msal.PublicClientApplication(client_id=client_id, authority=authority, token_cache=token_cache)
accounts = app.get_accounts()
if accounts:
    result = app.acquire_token_silent(scopes, account=accounts[0])
else:
    result = app.acquire_token_interactive(scopes)

with open(cache_file, "w") as f:
    f.write(token_cache.serialize())

if "access_token" not in result:
    print("❌ Auth failed")
    exit(1)

headers = {"Authorization": f"Bearer {result['access_token']}"}
# === RECURSIVE SECTION GROUP CRAWLER ===
def get_all_sections_from_group(group_id, group_name, headers):
    """Recursively get all sections from a section group and its nested groups"""
    sections = []
    
    try:
        # Get sections in this group
        group_sections_url = f"https://graph.microsoft.com/v1.0/sites/{SITE_ID}/onenote/sectionGroups/{group_id}/sections"
        group_sections_resp = requests.get(group_sections_url, headers=headers)
        
        if group_sections_resp.ok:
            group_sections = group_sections_resp.json().get("value", [])
            for s in group_sections:
                s["group"] = group_name
                s["group_id"] = group_id  # Keep for debugging/tracking
            sections.extend(group_sections)
        
        # Get nested section groups and recurse
        nested_groups_url = f"https://graph.microsoft.com/v1.0/sites/{SITE_ID}/onenote/sectionGroups/{group_id}/sectionGroups"
        nested_groups_resp = requests.get(nested_groups_url, headers=headers)
        
        if nested_groups_resp.ok:
            nested_groups = nested_groups_resp.json().get("value", [])
            for nested_group in nested_groups:
                # Recursively get sections from nested group
                nested_sections = get_all_sections_from_group(
                    nested_group["id"], 
                    nested_group["displayName"], 
                    headers
                )
                sections.extend(nested_sections)
                
    except requests.RequestException as e:
        print(f"⚠️ Error fetching sections from group {group_name}: {e}")
    
    return sections

# === LOAD NOTEBOOKS ===
notebooks_url = f"https://graph.microsoft.com/v1.0/sites/{SITE_ID}/onenote/notebooks"
notebooks_resp = requests.get(notebooks_url, headers=headers)
notebooks = notebooks_resp.json().get("value", []) if notebooks_resp.ok else []

if not notebooks:
    print("❌ No notebooks found.")
    exit(0)

# === ACCUMULATE CHUNKS ===
all_chunks = []

for nb in notebooks:
    print(f"\n📓 Notebook: {nb['displayName']}")

    # 1. Top-level sections
    sections_url = f"https://graph.microsoft.com/v1.0/sites/{SITE_ID}/onenote/notebooks/{nb['id']}/sections"
    sections_resp = requests.get(sections_url, headers=headers)
    sections = sections_resp.json().get("value", []) if sections_resp.ok else []

    # 2. Section group sections (recursive with improved function)
    sg_url = f"https://graph.microsoft.com/v1.0/sites/{SITE_ID}/onenote/notebooks/{nb['id']}/sectionGroups"
    sg_resp = requests.get(sg_url, headers=headers)
    
    if sg_resp.ok:
        for group in sg_resp.json().get("value", []):
            group_sections = get_all_sections_from_group(
                group["id"], 
                group["displayName"], 
                headers
            )
            sections.extend(group_sections)

    # === LOOP THROUGH ALL SECTIONS ===
    for section in sections:
        label = f"{section['displayName']}"
        if "group" in section:
            label += f" (Group: {section['group']})"
        print(f"\n📂 Section: {label}")

        pages_url = f"https://graph.microsoft.com/v1.0/sites/{SITE_ID}/onenote/sections/{section['id']}/pages"
        pages_resp = requests.get(pages_url, headers=headers)
        pages = pages_resp.json().get("value", []) if pages_resp.ok else []

        for page in pages:
            print(f"  📄 Page: {page['title']}")
            content_url = f"https://graph.microsoft.com/v1.0/sites/{SITE_ID}/onenote/pages/{page['id']}/content"
            content_resp = requests.get(content_url, headers=headers)

            if not content_resp.ok:
                print("     ❌ Could not fetch content")
                continue

            soup = BeautifulSoup(content_resp.text, "html.parser")
            text = soup.get_text().strip()
            if not text:
                print("     ⚠️ Empty page")
                continue

            # === CHUNK THE TEXT ===
            start = 0
            index = 0
            while start < len(text):
                end = start + CHUNK_SIZE
                chunk_text = text[start:end].strip()
                chunk = {
                    "text": chunk_text,
                    "metadata": {
                        "notebook": nb["displayName"],
                        "section": section["displayName"],
                        "page": page["title"],
                        "chunk_index": index,
                        "page_url": page.get("links", {}).get("oneNoteWebUrl", {}).get("href"),
                        "page_id": page["id"],  # Critical for change detection
                        "last_modified": page.get("lastModifiedDateTime")  # Critical for tracking
                    }
                }
                if "group" in section:
                    chunk["metadata"]["section_group"] = section["group"]

                all_chunks.append(chunk)
                start += CHUNK_SIZE - CHUNK_OVERLAP
                index += 1

            print(f"     🔹 {index} chunks created")

# === SAVE TO JSON ===
output_file = "onenote_chunks.json"
with open(output_file, "w", encoding="utf-8") as f:
    json.dump(all_chunks, f, indent=2, ensure_ascii=False)

print(f"\n✅ Saved {len(all_chunks)} chunks to {output_file}")