import os
import requests
import json
import msal
from datetime import datetime, timedelta
import pytz
from typing import List, Dict, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Microsoft Graph API Configuration - Same as working file
client_id = "f1547c8c-ac20-429e-bb35-bf70461505d6"
tenant_id = "25c282df-1bd0-4ae9-915e-ea3003bdd835"
authority = f"https://login.microsoftonline.com/{tenant_id}"
scopes = ["Sites.Read.All", "Notes.Read.All"]
site_id = "creospaninc.sharepoint.com,e403106b-31d0-4181-b487-2ef10c039907,c3316d29-5ff0-4cbb-965a-a57f407cd771"

notebook_id = "1-37a96d0e-c291-4cbe-8713-d163908468bb"

class ChangeDetector:
    def __init__(self):
        self.headers = None
        
    def authenticate(self) -> str:
        """Authenticate with Microsoft Graph API and return access token - Same as working file"""
        # === TOKEN CACHE ===
        cache_file = "msal_cache.json"
        token_cache = msal.SerializableTokenCache()
        if os.path.exists(cache_file):
            token_cache.deserialize(open(cache_file, "r").read())

        app = msal.PublicClientApplication(client_id=client_id, authority=authority, token_cache=token_cache)
        accounts = app.get_accounts()
        if accounts:
            result = app.acquire_token_silent(scopes, account=accounts[0])
        else:
            result = app.acquire_token_interactive(scopes)

        with open(cache_file, "w") as f:
            f.write(token_cache.serialize())

        if "access_token" not in result:
            print("❌ Auth failed")
            raise Exception("Authentication failed")

        self.headers = {"Authorization": f"Bearer {result['access_token']}"}
        return result['access_token']

    def get_cst_midnight_iso(self, days_ago: int = 1) -> str:
        """Get ISO timestamp for CST midnight N days ago"""
        central = pytz.timezone("America/Chicago")
        dt = datetime.now(central).replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=days_ago)
        return dt.astimezone(pytz.utc).strftime("%Y-%m-%dT%H:%M:%SZ")

    def get_sections(self, site_id: str, notebook_id: str) -> List[Dict]:
        """Get all sections in a notebook"""
        if not self.headers:
            self.authenticate()
            
        url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/onenote/notebooks/{notebook_id}/sections"
        response = requests.get(url, headers=self.headers)
        response.raise_for_status()
        return response.json().get("value", [])

    def get_section_groups(self, site_id: str, notebook_id: str) -> List[Dict]:
        """Get all section groups in a notebook"""
        if not self.headers:
            self.authenticate()
            
        url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/onenote/notebooks/{notebook_id}/sectionGroups"
        response = requests.get(url, headers=self.headers)
        response.raise_for_status()
        return response.json().get("value", [])

    def get_sections_in_group(self, site_id: str, group_id: str) -> List[Dict]:
        """Get all sections in a section group"""
        if not self.headers:
            self.authenticate()
            
        url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/onenote/sectionGroups/{group_id}/sections"
        response = requests.get(url, headers=self.headers)
        response.raise_for_status()
        return response.json().get("value", [])

    def get_nested_groups(self, site_id: str, group_id: str) -> List[Dict]:
        """Get nested section groups"""
        if not self.headers:
            self.authenticate()
            
        url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/onenote/sectionGroups/{group_id}/sectionGroups"
        response = requests.get(url, headers=self.headers)
        response.raise_for_status()
        return response.json().get("value", [])

    def collect_all_sections(self, site_id: str, notebook_id: str) -> List[Dict]:
        """Collect all sections including those in section groups"""
        all_sections = []

        # 1. Top-level sections
        all_sections.extend(self.get_sections(site_id, notebook_id))

        # 2. Recursively explore section groups
        def collect_from_group(group_id: str):
            sections = self.get_sections_in_group(site_id, group_id)
            all_sections.extend(sections)
            nested_groups = self.get_nested_groups(site_id, group_id)
            for nested_group in nested_groups:
                collect_from_group(nested_group["id"])

        top_level_groups = self.get_section_groups(site_id, notebook_id)
        for group in top_level_groups:
            collect_from_group(group["id"])

        return all_sections

    def get_pages_in_section(self, site_id: str, section_id: str, timestamp: str) -> List[Dict]:
        """Get pages in a section modified since timestamp"""
        if not self.headers:
            self.authenticate()
            
        url = (
            f"https://graph.microsoft.com/v1.0/sites/{site_id}/onenote/sections/{section_id}/pages"
            f"?$filter=lastModifiedDateTime ge {timestamp}"
        )
        response = requests.get(url, headers=self.headers)
        response.raise_for_status()
        return response.json().get("value", [])

    def detect_changes(self, days_ago: int = 1) -> List[Dict]:
        """Main function to detect changes in OneNote pages"""
        if not self.headers:
            self.authenticate()
            
        timestamp = self.get_cst_midnight_iso(days_ago)
        print(f"📅 Checking for changes since: {timestamp}")
        
        sections = self.collect_all_sections(site_id, notebook_id)
        all_updated_pages = []

        for section in sections:
            section_id = section["id"]
            section_name = section["displayName"]
            print(f"\n🔍 Checking section: {section_name}")

            try:
                pages = self.get_pages_in_section(site_id, section_id, timestamp)
                if pages:
                    print(f"✅ {len(pages)} updated page(s) in '{section_name}'")
                    # Add section info to each page
                    for page in pages:
                        page['section_name'] = section_name
                        page['section_id'] = section_id
                    all_updated_pages.extend(pages)
                else:
                    print("No changes in this section.")
            except requests.HTTPError as e:
                print(f"⚠️ Error fetching pages for '{section_name}': {e.response.status_code}")
                print(e.response.text)

        if not all_updated_pages:
            print("\n✅ No changes found in the notebook.")
        else:
            print(f"\n📄 Total updated pages: {len(all_updated_pages)}")
            for page in all_updated_pages:
                print(f"- {page['title']} | Last modified: {page['lastModifiedDateTime']}")
                print(f"  URL: {page['links']['oneNoteWebUrl']['href']}")
                print()

        return all_updated_pages

    def finalize(self):
        """Finalize change detection - upload any cache changes to S3"""
        # This method is no longer needed as authentication is handled directly
        pass

# === MAIN EXECUTION (for testing) ===
if __name__ == "__main__":
    detector = ChangeDetector()
    changes = detector.detect_changes()
    print(f"Found {len(changes)} changed pages") 