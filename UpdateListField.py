import requests
from msal import PublicClientApplication

# === CONFIG ===
CLIENT_ID = "073b50d3-d544-4140-816e-367dbe6c2641"
TENANT_ID = "25c282df-1bd0-4ae9-915e-ea3003bdd835"
AUTHORITY = f"https://login.microsoftonline.com/{TENANT_ID}"
SITE_ID = 'creospaninc.sharepoint.com,e403106b-31d0-4181-b487-2ef10c039907,c3316d29-5ff0-4cbb-965a-a57f407cd771'
LIST_ID = 'fe0a99ad-736e-4d09-a4dd-fbb0b4e50120'  # Replace with the actual list ID
NEW_STATUS = 'Not Started'

# AUTH SETUP
AUTHORITY = f"https://login.microsoftonline.com/{TENANT_ID}"
SCOPE = ["Sites.ReadWrite.All"]
app = PublicClientApplication(CLIENT_ID, authority=AUTHORITY)
flow = app.initiate_device_flow(scopes=SCOPE)
print(flow["message"])  # Shows device login instructions
result = app.acquire_token_by_device_flow(flow)

if "access_token" not in result:
    print("Authentication failed.")
    exit()

headers = {
    'Authorization': f"Bearer {result['access_token']}",
    'Content-Type': 'application/json'
}

columns_url = f"https://graph.microsoft.com/v1.0/sites/{SITE_ID}/lists/{LIST_ID}/columns"
columns_response = requests.get(columns_url, headers=headers)

if columns_response.ok:
    for col in columns_response.json()["value"]:
        print(f"{col['name']} — {col.get('displayName')} ({col.get('columnType')})")
else:
    print("Error getting columns:", columns_response.text)



# STEP 1: Get all list items
items_url = f"https://graph.microsoft.com/v1.0/sites/{SITE_ID}/lists/{LIST_ID}/items?$expand=fields"
items_response = requests.get(items_url, headers=headers)

if items_response.status_code != 200:

    print("Error fetching list items:", items_response.text)
    exit()





items = items_response.json().get("value", [])

# Print all fields for the first item to inspect the actual field names
sample_item = items[0]
print("Inspecting fields for item:", sample_item["fields"].get("Title", "(no title)"))
for k, v in sample_item["fields"].items():
    print(f"{k}: {v}")

# STEP 2: Loop through each item and update the Status
for item in items:
    item_id = item["id"]
    title = item["fields"].get("Title", "(no title)")

    update_url = f"https://graph.microsoft.com/v1.0/sites/{SITE_ID}/lists/{LIST_ID}/items/{item_id}/fields"
    payload = {
        "Progress": "Not Started"  # 'Progress' is the internal name of the 'Status' field
    }
    # First write a placeholder value
    requests.patch(update_url, headers=headers, json={"Progress": "..."})
    # Then write the actual value
    requests.patch(update_url, headers=headers, json={"Progress": "Not Started"})
    response = requests.patch(update_url, headers=headers, json=payload)

    if response.status_code == 200:
        print(f"✅ Updated '{title}' to Status: Not Started")
    else:
        print(f"❌ Failed to update '{title}': {response.status_code} - {response.text}")
    response = requests.patch(update_url, headers=headers, json=payload)

    if response.status_code == 200:
        print(f"✅ Updated '{title}' to Status: {NEW_STATUS}")
    else:
        print(f"❌ Failed to update '{title}': {response.status_code} - {response.text}")