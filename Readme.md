# Knowledge Pipeline - AWS Lambda Deployment

This project deploys a knowledge pipeline that automatically refreshes embeddings from Microsoft OneNote and stores them in S3 for use in a RAG chatbot.

## 🚀 Features

- **Automated Change Detection**: Monitors OneNote for updates using Microsoft Graph API
- **Content Extraction**: Extracts and chunks updated content from OneNote pages
- **Embedding Generation**: Creates FAISS embeddings using OpenAI's text-embedding-3-small
- **S3 Storage**: Uploads embeddings and metadata to timestamped S3 folders
- **Scheduled Execution**: Runs daily at 1 AM CST via EventBridge
- **Clean Index Rebuild**: Always rebuilds the FAISS index from scratch to avoid stale vectors

## 📋 Prerequisites

- AWS CLI configured with appropriate permissions
- Node.js (v16+) and npm
- Python 3.9+
- Docker (for Lambda container)
- AWS CDK CLI: `npm install -g aws-cdk`

## 🔧 Setup

### 1. Environment Variables

Create a `.env` file in the project root with the following variables:

```bash
# OpenAI API
OPENAI_API_KEY=your_openai_api_key

# AWS Credentials
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
S3_BUCKET_NAME=your_s3_bucket_name

# Microsoft Graph API (OneNote)
MS_CLIENT_ID=your_ms_client_id
MS_TENANT_ID=your_ms_tenant_id
MS_SITE_ID=your_ms_site_id
MS_NOTEBOOK_ID=your_ms_notebook_id
```

### 2. Microsoft Graph API Setup

1. Register an application in Azure AD
2. Grant `Notes.Read.All` and `Sites.Read.All` permissions
3. Get the client ID, tenant ID, site ID, and notebook ID

### 3. S3 Bucket

Ensure your S3 bucket exists and is accessible with the provided credentials.

## 🚀 Deployment

### Quick Deploy

Run the deployment script:

```bash
./deploy.sh
```

### Manual Deploy

1. Install dependencies:
```bash
npm install
pip3 install -r requirements.txt
```

2. Build the CDK project:
```bash
npm run build
```

3. Deploy to AWS:
```bash
npx cdk deploy
```

## 📊 Architecture

```
EventBridge (Daily 1 AM CST)
    ↓
Lambda Function (Docker Container)
    ↓
Microsoft Graph API → OneNote Content
    ↓
OpenAI Embeddings → FAISS Index
    ↓
S3 Storage (timestamped folders)
    ↓
latest.json (pointer to latest)
```

## 📁 S3 Structure

```
s3://your-bucket/
├── rag-knowledge/
│   ├── latest.json
│   ├── 2025-06-19/
│   │   ├── index.faiss
│   │   └── metadata.json
│   └── 2025-06-20/
│       ├── index.faiss
│       └── metadata.json
```

## 🔍 Monitoring

- **CloudWatch Logs**: Check Lambda function logs for execution details
- **S3**: Monitor new embeddings being uploaded
- **EventBridge**: Verify scheduled executions

## 🧪 Testing Locally

Test the pipeline locally before deployment:

```bash
python3 knowledge_pipeline.py
```

## 🔧 Configuration

### Lambda Settings

- **Timeout**: 15 minutes (configurable in `cdk.ts`)
- **Memory**: 2048 MB (configurable in `cdk.ts`)
- **Runtime**: Python 3.9 (Docker container)

### Pipeline Settings

- **Chunk Size**: 1000 characters (configurable in `content_extractor.py`)
- **Chunk Overlap**: 200 characters (configurable in `content_extractor.py`)
- **Embedding Model**: text-embedding-3-small (configurable in `embedding_generator.py`)

## 🛠️ Troubleshooting

### Common Issues

1. **Authentication Errors**: Check Microsoft Graph API credentials
2. **S3 Upload Failures**: Verify S3 bucket permissions
3. **Timeout Errors**: Increase Lambda timeout in `cdk.ts`
4. **Memory Errors**: Increase Lambda memory in `cdk.ts`

### Logs

Check CloudWatch logs for detailed error messages:

```bash
aws logs describe-log-groups --log-group-name-prefix "/aws/lambda/KnowledgePipeline"
```

## 📈 Scaling

- **Large Content**: Increase Lambda memory and timeout
- **Frequent Updates**: Consider running multiple times per day
- **Multiple Notebooks**: Extend the pipeline to handle multiple OneNote notebooks

## 🔒 Security

- All credentials are stored as Lambda environment variables
- S3 bucket access is restricted via IAM roles
- Microsoft Graph API uses OAuth 2.0 authentication

## 📝 License

This project is for internal use at Creospan.

## 📋 **TASK LIST: OneNote to S3 Knowledge Pipeline**

### **Phase 1: Core Infrastructure & Configuration**
1. **Create AWS Infrastructure Setup**
   - Set up S3 bucket for storing embeddings and metadata
   - Configure IAM roles and permissions for Lambda/EC2
   - Set up EventBridge rule for daily 1 AM execution
   - Create CloudWatch logging and monitoring

2. **Create Configuration Management**
   - Create `config.py` with all environment variables and settings
   - Set up AWS credentials and region configuration
   - Configure S3 bucket names and paths
   - Set up Microsoft Graph API credentials

### **Phase 2: State Management & Change Detection**
3. **Create State Tracking System**
   - Create `state_manager.py` to track last processed timestamps
   - Implement local JSON file for storing last known state
   - Add S3-based state backup for redundancy
   - Create state comparison logic for detecting changes

4. **Enhance Change Detection Logic**
   - Refactor `query_notebook_changes.py` into modular functions
   - Add specific section targeting (knowledge base only)
   - Implement incremental change detection
   - Add error handling and retry logic

### **Phase 3: Content Processing Pipeline**
5. **Create Content Extraction Module**
   - Refactor `1-All_OneNote_Sections_To_Json_chunks.py` into `content_extractor.py`
   - Add selective page extraction (only changed pages)
   - Implement content chunking with configurable parameters
   - Add content validation and cleaning

6. **Create Embedding Generation Module**
   - Refactor `2-embed_using_FAISS.py` into `embedding_generator.py`

### **Phase 4: S3 Storage & Management**
7. **Create S3 Storage Manager**
   - Create `s3_manager.py` for all S3 operations
   - Implement timestamped folder structure
   - Add file upload with proper metadata
   - Create `latest.json` management system

8. **Create Metadata Management**
   - Design metadata schema for documents
   - Implement metadata generation from OneNote content
   - Add source URL and page ID tracking
   - Create metadata validation

### **Phase 5: Main Pipeline Orchestration**
9. **Create Main Pipeline Script**
   - Create `knowledge_pipeline.py` as the main orchestrator
   - Implement step-by-step execution flow
   - Add comprehensive error handling
   - Create rollback mechanisms for failed runs

10. **Add Monitoring & Logging**
    - Implement structured logging throughout the pipeline
    - Add CloudWatch metrics and alarms
    - Create execution status tracking
    - Add performance monitoring

### **Phase 6: Testing & Deployment**
11. **Create Testing Framework**
    - Unit tests for each module
    - Integration tests for the full pipeline
    - Mock OneNote API responses
    - Test S3 operations with local files

12. **Create Deployment Package**
    - Create `requirements.txt` with all dependencies
    - Create Docker container (if using Lambda)
    - Create deployment scripts
    - Add environment-specific configurations

### **Phase 7: Integration & Optimization**
13. **Integrate with Existing RAG System**
    - Update `main.py` to read from S3 instead of local files
    - Add S3 path resolution from `latest.json`
    - Implement fallback to local files if S3 fails
    - Add cache warming mechanisms

14. **Performance Optimization**
    - Implement parallel processing where possible
    - Add caching for frequently accessed data
    - Optimize FAISS index creation
    - Add incremental index updates

### **Phase 8: Production Readiness**
15. **Add Production Features**
    - Implement health checks and monitoring
    - Add alerting for pipeline failures
    - Create backup and recovery procedures
    - Add data retention policies

16. **Documentation & Maintenance**
    - Create comprehensive README
    - Add troubleshooting guides
    - Create maintenance procedures
    - Add performance tuning guidelines

