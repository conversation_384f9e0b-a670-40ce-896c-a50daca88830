import * as cdk from 'aws-cdk-lib';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as events from 'aws-cdk-lib/aws-events';
import * as targets from 'aws-cdk-lib/aws-events-targets';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as s3 from 'aws-cdk-lib/aws-s3';
import { Construct } from 'constructs';

export class KnowledgePipelineStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);

    // S3 Bucket - Use existing bucket
    const bucketName = 'knowledgebot-faiss-embedding';
    const knowledgeBucket = s3.Bucket.fromBucketName(this, 'KnowledgeBucket', bucketName);

    // IAM Role for Lambda
    const lambdaRole = new iam.Role(this, 'KnowledgePipelineLambdaRole', {
      assumedBy: new iam.ServicePrincipal('lambda.amazonaws.com'),
      managedPolicies: [
        iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole'),
      ],
      inlinePolicies: {
        S3FullAccess: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: ['s3:GetObject', 's3:PutObject', 's3:DeleteObject', 's3:ListBucket'],
              resources: [knowledgeBucket.bucketArn, `${knowledgeBucket.bucketArn}/*`],
            }),
          ],
        }),
        SNSPublishAccess: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: ['sns:Publish'],
              resources: ['arn:aws:sns:us-east-1:203918886422:embedding-updates'],
            }),
          ],
        }),
      },
    });

    // Add ECR permissions for future Docker support
    lambdaRole.addToPolicy(new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: ['ecr:GetAuthorizationToken', 'ecr:BatchCheckLayerAvailability', 'ecr:GetDownloadUrlForLayer', 'ecr:BatchGetImage'],
      resources: ['*'],
    }));

    // Lambda function - ZIP deployment with minimal package
    const knowledgePipelineLambda = new lambda.Function(this, 'KnowledgePipelineFunction', {
      runtime: lambda.Runtime.PYTHON_3_9,
      handler: 'knowledge_pipeline.lambda_handler',
      code: lambda.Code.fromAsset('lambda_package'),
      role: lambdaRole,
      timeout: cdk.Duration.minutes(15),
      memorySize: 2048,
      environment: {
        OPENAI_API_KEY: process.env.OPENAI_API_KEY || '',
        S3_BUCKET_NAME: bucketName,
      },
    });

    // EventBridge Rule
    const rule = new events.Rule(this, 'KnowledgePipelineSchedule', {
      schedule: events.Schedule.cron({ minute: '0', hour: '7', day: '*', month: '*', year: '*' }),
      description: 'Trigger knowledge pipeline daily at 1 AM CST',
    });

    rule.addTarget(new targets.LambdaFunction(knowledgePipelineLambda));

    // Outputs
    new cdk.CfnOutput(this, 'LambdaFunctionName', {
      value: knowledgePipelineLambda.functionName,
      description: 'Knowledge Pipeline Lambda Function',
    });
  }
}
