# =============================================================================
# Knowledge Exchange Bot - Environment Configuration
# =============================================================================
# Copy this file to .env and fill in your actual values
# Never commit the .env file with real credentials to version control
# =============================================================================

# OpenAI Configuration
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-your-openai-api-key-here

# AWS Configuration
# For local development only - Lambda uses IAM roles in production
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_DEFAULT_REGION=us-east-1

# S3 Storage
# Bucket name for storing embeddings, cache, and artifacts
S3_BUCKET_NAME=your-s3-bucket-name

# Microsoft Graph API Configuration
# Register an app in Azure AD to get these values
# App registration guide: https://docs.microsoft.com/en-us/graph/auth-register-app-v2

# Azure AD Application (Client) ID
MS_CLIENT_ID=your-azure-app-client-id

# Azure AD Directory (Tenant) ID  
MS_TENANT_ID=your-azure-tenant-id

# SharePoint Site ID (format: domain,site-id,web-id)
# Find this in SharePoint admin center or Graph Explorer
MS_SITE_ID=your-sharepoint-site-id

# =============================================================================
# Optional Configuration (with defaults)
# =============================================================================

# Lambda Environment Detection (automatically set in AWS Lambda)
# AWS_LAMBDA_FUNCTION_NAME=knowledge-pipeline-function

# =============================================================================
# Development/Testing Configuration
# =============================================================================

# Set to 'true' for development mode with verbose logging
# DEBUG=false

# Local file paths (for development)
# LOCAL_CACHE_DIR=./cache
# LOCAL_DATA_DIR=./data

# =============================================================================
# Microsoft Graph API Scopes (predefined in code)
# =============================================================================
# The following scopes are used by the application:
# - Sites.Read.All (to read SharePoint/OneNote content)
# - Files.Read.All (to access OneNote files)
# These are configured in the Azure app registration

# =============================================================================
# Setup Instructions:
# =============================================================================
# 1. Copy this file: cp example.env .env
# 2. Fill in all required values above
# 3. Ensure .env is in your .gitignore (it should be)
# 4. Test your configuration: python test_docker_lambda.py
# ============================================================================= 