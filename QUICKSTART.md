# 🚀 Quick Start Guide

Get the Knowledge Exchange Bot up and running in 5 minutes!

## Prerequisites

- **Python 3.8+** - [Download here](https://python.org/downloads/)
- **Node.js 14+** - [Download here](https://nodejs.org/)
- **AWS CLI** - [Install guide](https://aws.amazon.com/cli/)
- **Docker** - [Install guide](https://docker.com/get-started/)

## 🏃‍♂️ Quick Setup (Automated)

```bash
# Clone the repository
git clone https://github.com/creospan-inc/knowledge_exchange_bot.git
cd knowledge_exchange_bot

# Run the automated setup script
./setup.sh
```

## ⚙️ Manual Setup

### 1. Environment Configuration

```bash
# Copy the example environment file
cp example.env .env

# Edit with your actual values
nano .env  # or use your preferred editor
```

### 2. Required Environment Variables

Fill in these **required** values in your `.env` file:

```env
# OpenAI API Key (required)
OPENAI_API_KEY=sk-your-openai-api-key-here

# Microsoft Graph API (required)
MS_CLIENT_ID=your-azure-app-client-id
MS_TENANT_ID=your-azure-tenant-id
MS_SITE_ID=your-sharepoint-site-id

# AWS Configuration (required)
S3_BUCKET_NAME=your-s3-bucket-name
AWS_DEFAULT_REGION=us-east-1
```

### 3. Install Dependencies

```bash
# Create Python virtual environment
python3 -m venv venv
source venv/bin/activate

# Install Python dependencies
pip install -r requirements.txt

# Install Node.js dependencies
npm install
```

### 4. Build the Project

```bash
# Build CDK project
npm run build
```

## 🧪 Test Your Setup

### Local Test

```bash
# Test the Docker Lambda function locally
python test_docker_lambda.py
```

### Expected Output
```
✅ Docker container started successfully
✅ Knowledge pipeline completed successfully
📊 Results: X pages processed, Y chunks generated
```

## 🚀 Deploy to AWS

### 1. Configure AWS CLI

```bash
aws configure
# Enter your AWS Access Key, Secret Key, and Region
```

### 2. Deploy Infrastructure

```bash
# Deploy the complete stack
cdk deploy

# Follow the prompts and confirm deployment
```

### 3. Verify Deployment

```bash
# Check CloudWatch logs
aws logs describe-log-groups --log-group-name-prefix /aws/lambda/KnowledgePipeline

# Test the deployed function
aws lambda invoke --function-name YourFunctionName response.json
```

## 📝 Getting Your API Keys

### OpenAI API Key
1. Go to [OpenAI Platform](https://platform.openai.com/api-keys)
2. Sign in and create a new API key
3. Copy the key (starts with `sk-`)

### Microsoft Graph API
1. Go to [Azure Portal](https://portal.azure.com)
2. Navigate to **Azure Active Directory** > **App registrations**
3. Click **New registration**
4. Configure permissions: `Sites.Read.All`, `Notes.Read.All`
5. Get your **Application (client) ID** and **Directory (tenant) ID**

### SharePoint Site ID
1. Go to your SharePoint site
2. Use Graph Explorer: `https://graph.microsoft.com/v1.0/sites/root`
3. Or use PowerShell: `Get-PnPSite -Includes Id`

## 🔧 Troubleshooting

### Common Issues

**❌ "Missing environment variables"**
- Ensure all required variables are set in `.env`
- Check for typos in variable names

**❌ "Docker build failed"**
- Ensure Docker is running
- Check if you're using the correct architecture (x86_64)

**❌ "CDK deploy failed"**
- Verify AWS CLI is configured correctly
- Check if you have sufficient AWS permissions

**❌ "Authentication failed"**
- Verify Microsoft Graph API credentials
- Ensure app permissions are granted by admin

### Getting Help

1. **Check the logs**: Always check CloudWatch logs for detailed error messages
2. **Read the full README**: Complete documentation in [README.md](README.md)
3. **Review experience docs**: Detailed guides in the `docs/` folder

## 📚 Next Steps

### Explore the Documentation
- [README.md](README.md) - Complete project documentation
- [docs/performance-optimization-experience.md](docs/performance-optimization-experience.md) - Performance insights
- [docs/aws-services-integration-experience.md](docs/aws-services-integration-experience.md) - Architecture overview

### Customize for Your Organization
- Modify the notebook and site configurations
- Adjust the scheduling frequency in `cdk.ts`
- Customize notification messages in `knowledge_pipeline.py`

### Monitor and Scale
- Set up CloudWatch alarms
- Configure SNS notifications
- Monitor costs in AWS Cost Explorer

---

**🎉 You're all set!** Your Knowledge Exchange Bot is now ready to automatically process OneNote content and generate embeddings for intelligent search.

For detailed documentation and advanced configuration, see the main [README.md](README.md) file. 