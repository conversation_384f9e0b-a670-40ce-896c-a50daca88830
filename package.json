{"name": "knowledge-pipeline-cdk", "version": "1.0.0", "description": "CDK infrastructure for Knowledge Pipeline Lambda", "main": "bin/app.js", "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk", "deploy": "cdk deploy", "destroy": "cdk destroy", "diff": "cdk diff", "synth": "cdk synth"}, "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "20.0.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "aws-cdk": "2.100.0", "ts-node": "^10.9.0", "typescript": "~5.0.0"}, "dependencies": {"aws-cdk-lib": "2.100.0", "constructs": "^10.0.0", "source-map-support": "^0.5.21"}}