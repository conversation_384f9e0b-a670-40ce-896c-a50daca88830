# === SENSITIVE FILES ===
.env
*.env
.env.local
.env.production
.env.staging

# === AUTHENTICATION & CACHE FILES ===
msal_cache.json
msal_token_cache.bin
*.token
*.credentials

# === AWS CDK BUILD ARTIFACTS ===
cdk.out/
*.d.ts
*.js
*.js.map
!bin/*.js
!lambda/*.js
node_modules/

# === PYTHON ===
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# === VIRTUAL ENVIRONMENTS ===
.env/
.venv/
env/
venv/
ENV/
env.bak/
venv.bak/

# === FAISS & MODEL FILES ===
*.index
faiss.index
faiss_metadata.json
*.faiss
*.bin
*.model

# === DATA FILES ===
*.json
!package.json
!package-lock.json
!cdk.json
!tsconfig.json
!*.template.json

# === TEMPORARY FILES ===
/tmp/
*.tmp
*.temp
response.json
test-response.json
test_payload.json

# === IDEs ===
.vscode/
.idea/
*.swp
*.swo
*~

# === OS GENERATED ===
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# === LOGS ===
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# === WORKING DIRECTORY ===
working/

# === TEST FILES ===
test_*.py
!test_docker_lambda.py
