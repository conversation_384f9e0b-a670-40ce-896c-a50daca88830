import os
import requests
import json
from datetime import datetime
from dotenv import load_dotenv
import msal

# === Load API Keys ===
load_dotenv()
NOTION_API_KEY = os.getenv("NOTION_API_KEY")
NOTION_DATABASE_ID = os.getenv("NOTION_DATABASE_ID")
MS_CLIENT_ID = os.getenv("MS_CLIENT_ID")
MS_TENANT_ID = os.getenv("MS_TENANT_ID")

# === Notion API Setup ===
NOTION_HEADERS = {
    "Authorization": f"Bearer {NOTION_API_KEY}",
    "Content-Type": "application/json",
    "Notion-Version": "2022-06-28"
}

# === Microsoft Graph Auth ===
def get_graph_token():
    authority = f"https://login.microsoftonline.com/{MS_TENANT_ID}"
    scopes = ["Notes.ReadWrite", "User.Read"]
    cache = msal.SerializableTokenCache()
    app = msal.PublicClientApplication(MS_CLIENT_ID, authority=authority, token_cache=cache)

    accounts = app.get_accounts()
    if accounts:
        result = app.acquire_token_silent(scopes, account=accounts[0])
    else:
        flow = app.initiate_device_flow(scopes=scopes)
        print(flow["message"])
        result = app.acquire_token_by_device_flow(flow)

    if "access_token" in result:
        return result["access_token"]
    else:
        raise Exception(f"Failed to get access token: {result.get('error_description')}")

# === Step 1: Get Notion Pages ===
def get_notion_tasks():
    url = f"https://api.notion.com/v1/databases/{NOTION_DATABASE_ID}/query"
    tasks = {}
    next_cursor = None

    while True:
        payload = {"start_cursor": next_cursor} if next_cursor else {}
        response = requests.post(url, headers=NOTION_HEADERS, json=payload)
        data = response.json()

        if "results" not in data:
            raise Exception(f"Notion error: {data}")

        for page in data["results"]:
            title_obj = page["properties"]["Name"]["title"]
            title = title_obj[0]["plain_text"] if title_obj else "Untitled"
            tasks[title] = page["id"]

        if not data.get("has_more"):
            break
        next_cursor = data.get("next_cursor")

    return tasks

# === Step 2: Get Page Content ===
def get_notion_page_blocks(page_id):
    url = f"https://api.notion.com/v1/blocks/{page_id}/children"
    blocks = []
    next_cursor = None

    while True:
        params = {"start_cursor": next_cursor} if next_cursor else {}
        response = requests.get(url, headers=NOTION_HEADERS, params=params)
        data = response.json()
        blocks += data.get("results", [])

        if not data.get("has_more"):
            break
        next_cursor = data.get("next_cursor")

    return blocks

# === Step 3: Convert Blocks to HTML ===
def notion_blocks_to_html(blocks):
    html = ""
    for block in blocks:
        block_type = block["type"]
        text = block.get(block_type, {}).get("text", [])

        if block_type == "paragraph":
            html += "<p>" + "".join([t["plain_text"] for t in text]) + "</p>\n"
        elif block_type.startswith("heading_"):
            level = block_type.split("_")[-1]
            html += f"<h{level}>{text[0]['plain_text']}</h{level}>\n" if text else ""
        elif block_type == "to_do":
            checked = "☑️" if block[block_type]["checked"] else "☐"
            html += f"<p>{checked} " + "".join([t["plain_text"] for t in text]) + "</p>\n"
        elif block_type == "bulleted_list_item":
            html += f"<ul><li>{text[0]['plain_text']}</li></ul>\n" if text else ""
        elif block_type == "numbered_list_item":
            html += f"<ol><li>{text[0]['plain_text']}</li></ol>\n" if text else ""
        else:
            html += f"<p>[{block_type} block not supported]</p>\n"
    return html

# === Step 4: Create OneNote Page ===
def create_onenote_page(title, content_html, token):
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/xhtml+xml"
    }

    full_page = f"""
    <!DOCTYPE html>
    <html>
      <head>
        <title>{title}</title>
        <meta name="created" content="{datetime.utcnow().isoformat()}Z"/>
      </head>
      <body>
        {content_html}
      </body>
    </html>
    """

    url = "https://graph.microsoft.com/v1.0/me/onenote/pages"
    response = requests.post(url, headers=headers, data=full_page)

    if response.status_code == 201:
        print(f"✅ Created OneNote page: {title}")
    else:
        print(f"❌ Failed to create page '{title}': {response.status_code}")
        print(response.text)

# === Main Sync Logic ===
if __name__ == "__main__":
    print("🔄 Starting Notion → OneNote sync...")
    access_token = get_graph_token()
    tasks = get_notion_tasks()

    for title, page_id in tasks.items():
        print(f"📄 Syncing: {title}")
        blocks = get_notion_page_blocks(page_id.replace("-", ""))
        html_content = notion_blocks_to_html(blocks)
        create_onenote_page(title, html_content, access_token)