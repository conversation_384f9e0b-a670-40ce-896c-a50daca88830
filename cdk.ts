import * as cdk from 'aws-cdk-lib';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as events from 'aws-cdk-lib/aws-events';
import * as targets from 'aws-cdk-lib/aws-events-targets';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as s3 from 'aws-cdk-lib/aws-s3';
import { Construct } from 'constructs';

export class KnowledgePipelineStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);

    // S3 Bucket - Use existing bucket instead of creating new one
    const bucketName = 'knowledgebot-faiss-embedding';
    const knowledgeBucket = s3.Bucket.fromBucketName(this, 'KnowledgeBucket', bucketName);

    // IAM Role for Lambda
    const lambdaRole = new iam.Role(this, 'KnowledgePipelineLambdaRole', {
      assumedBy: new iam.ServicePrincipal('lambda.amazonaws.com'),
      managedPolicies: [
        iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole'),
      ],
      inlinePolicies: {
        S3FullAccess: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                's3:GetObject',
                's3:PutObject',
                's3:DeleteObject',
                's3:ListBucket',
              ],
              resources: [
                knowledgeBucket.bucketArn,
                `${knowledgeBucket.bucketArn}/*`,
              ],
            }),
          ],
        }),
        SNSPublishAccess: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                'sns:Publish',
              ],
              resources: [
                'arn:aws:sns:us-east-1:203918886422:embedding-updates',
              ],
            }),
          ],
        }),
      },
    });

    // Add S3 permissions
    knowledgeBucket.grantReadWrite(lambdaRole);

    // Add CloudWatch Logs permissions
    lambdaRole.addToPolicy(new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'logs:CreateLogGroup',
        'logs:CreateLogStream',
        'logs:PutLogEvents',
      ],
      resources: ['*'],
    }));

    // Lambda function - Docker deployment for better dependency management
    const knowledgePipelineLambda = new lambda.DockerImageFunction(this, 'KnowledgePipelineFunction', {
      code: lambda.DockerImageCode.fromImageAsset('.', {
        cmd: ['knowledge_pipeline.lambda_handler'],
      }),
      role: lambdaRole,
      timeout: cdk.Duration.minutes(15),
      memorySize: 2048,
      environment: {
        OPENAI_API_KEY: process.env.OPENAI_API_KEY || '',
        S3_BUCKET_NAME: bucketName,
      },
    });

    // EventBridge Rule to trigger Lambda daily at 1 AM CST (7 AM UTC)
    const rule = new events.Rule(this, 'KnowledgePipelineSchedule', {
      schedule: events.Schedule.cron({
        minute: '0',
        hour: '7', // 1 AM CST = 7 AM UTC
        day: '*',
        month: '*',
        year: '*',
      }),
      description: 'Trigger knowledge pipeline daily at 1 AM CST',
    });

    // Add Lambda as target for the EventBridge rule
    rule.addTarget(new targets.LambdaFunction(knowledgePipelineLambda));

    // Output the bucket name
    new cdk.CfnOutput(this, 'KnowledgeBucketName', {
      value: bucketName,
      description: 'S3 Bucket for storing knowledge embeddings',
    });

    // Output the Lambda function name
    new cdk.CfnOutput(this, 'LambdaFunctionName', {
      value: knowledgePipelineLambda.functionName,
      description: 'Knowledge Pipeline Lambda Function',
    });
  }
} 