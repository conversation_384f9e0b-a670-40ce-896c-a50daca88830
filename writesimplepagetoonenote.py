import msal
import requests
import os
from datetime import datetime
import pytz
import jwt

# === CONFIG ===
client_id = "073b50d3-d544-4140-816e-367dbe6c2641"
tenant_id = "25c282df-1bd0-4ae9-915e-ea3003bdd835"
authority = f"https://login.microsoftonline.com/{tenant_id}"

scopes = [
    "https://graph.microsoft.com/Notes.Create",
    "https://graph.microsoft.com/Notes.ReadWrite",
    "https://graph.microsoft.com/User.Read"
]

# === TOKEN SETUP ===
cache_file = "token_cache.bin"
token_cache = msal.SerializableTokenCache()
if os.path.exists(cache_file):
    token_cache.deserialize(open(cache_file, "r").read())

app = msal.PublicClientApplication(
    client_id=client_id,
    authority=authority,
    token_cache=token_cache
)

accounts = app.get_accounts()
if accounts:
    token_response = app.acquire_token_silent(scopes, account=accounts[0])
else:
    flow = app.initiate_device_flow(scopes=scopes)
    if "user_code" not in flow:
        raise Exception("Failed to create device flow")
    print("🔐", flow["message"])
    token_response = app.acquire_token_by_device_flow(flow)

# Save updated token cache
if token_cache.has_state_changed:
    with open(cache_file, "w") as f:
        f.write(token_cache.serialize())

# === TOKEN CHECK ===
if not token_response or "access_token" not in token_response:
    print("❌ Failed to acquire token. Details:", token_response)
    exit(1)

decoded = jwt.decode(token_response["access_token"], options={"verify_signature": False})
print("🔍 Token audience (aud):", decoded.get("aud"))

# === TIME ===
central = pytz.timezone("America/Chicago")
current_time = datetime.now(central)
timestamp_iso = current_time.strftime("%Y-%m-%dT%H:%M:%S%z")
pretty_time = current_time.strftime("%A, %B %d, %Y at %I:%M %p %Z")

# === PAGE CONTENT ===
html_content = f"""
<!DOCTYPE html>
<html>
    <head>
        <title>My Custom Template Page</title>
        <meta name="created" content="{timestamp_iso}"/>
    </head>
    <body>
        <h1>📝 My Custom Template</h1>
        <p><strong>Created:</strong> {pretty_time}</p>
        <p>This is a custom template page created in OneNote using Microsoft Graph API.</p>
        <hr/>
        <h2>🔹 Tasks</h2>
        <ul>
            <li>☐ Task 1</li>
            <li>☐ Task 2</li>
        </ul>
        <h2>🧠 Notes</h2>
        <p>...</p>
    </body>
</html>
"""

# === POST PAGE TO DEFAULT NOTEBOOK/SECTION ===
page_url = "https://graph.microsoft.com/v1.0/me/onenote/pages"
page_response = requests.post(
    page_url,
    headers={
        "Authorization": f"Bearer {token_response['access_token']}",
        "Content-Type": "application/xhtml+xml"
    },
    data=html_content
)

if page_response.status_code == 201:
    print("✅ Page created successfully in default notebook!")
else:
    print("❌ Failed to create page:", page_response.status_code, page_response.text)