import os
import requests
import msal
from datetime import datetime, timedelta
import pytz

# === CONFIG ===
client_id = "073b50d3-d544-4140-816e-367dbe6c2641"
tenant_id = "25c282df-1bd0-4ae9-915e-ea3003bdd835"
authority = f"https://login.microsoftonline.com/{tenant_id}"
scope = ["Notes.Read.All", "Sites.Read.All"]
token_cache_file = "msal_token_cache.bin"

site_id = "creospaninc.sharepoint.com,e403106b-31d0-4181-b487-2ef10c039907,c3316d29-5ff0-4cbb-965a-a57f407cd771"
notebook_id = "1-37a96d0e-c291-4cbe-8713-d163908468bb"

# === TIME UTIL ===
def get_cst_midnight_iso(days_ago=2):
    central = pytz.timezone("America/Chicago")
    dt = datetime.now(central).replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=days_ago)
    return dt.astimezone(pytz.utc).strftime("%Y-%m-%dT%H:%M:%SZ")

# === AUTH ===
def get_token():
    cache = msal.SerializableTokenCache()
    if os.path.exists(token_cache_file):
        cache.deserialize(open(token_cache_file, "r").read())

    app = msal.PublicClientApplication(client_id, authority=authority, token_cache=cache)

    accounts = app.get_accounts()
    if accounts:
        result = app.acquire_token_silent(scope, account=accounts[0])
    else:
        flow = app.initiate_device_flow(scopes=scope)
        if "user_code" not in flow:
            raise ValueError("Failed to initiate device flow")
        print(flow["message"])
        result = app.acquire_token_by_device_flow(flow)

    if cache.has_state_changed:
        with open(token_cache_file, "w") as f:
            f.write(cache.serialize())

    if "access_token" not in result:
        raise RuntimeError("Authentication failed: " + result.get("error_description", "Unknown error"))

    return result["access_token"]

# === ONE NOTE NAVIGATION ===
def get_sections(access_token, site_id, notebook_id):
    url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/onenote/notebooks/{notebook_id}/sections"
    headers = {"Authorization": f"Bearer {access_token}"}
    response = requests.get(url, headers=headers)
    response.raise_for_status()
    return response.json().get("value", [])

def get_section_groups(access_token, site_id, notebook_id):
    url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/onenote/notebooks/{notebook_id}/sectionGroups"
    headers = {"Authorization": f"Bearer {access_token}"}
    response = requests.get(url, headers=headers)
    response.raise_for_status()
    return response.json().get("value", [])

def get_sections_in_group(access_token, site_id, group_id):
    url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/onenote/sectionGroups/{group_id}/sections"
    headers = {"Authorization": f"Bearer {access_token}"}
    response = requests.get(url, headers=headers)
    response.raise_for_status()
    return response.json().get("value", [])

def get_nested_groups(access_token, site_id, group_id):
    url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/onenote/sectionGroups/{group_id}/sectionGroups"
    headers = {"Authorization": f"Bearer {access_token}"}
    response = requests.get(url, headers=headers)
    response.raise_for_status()
    return response.json().get("value", [])

def collect_all_sections(access_token, site_id, notebook_id):
    all_sections = []

    # 1. Top-level sections
    all_sections.extend(get_sections(access_token, site_id, notebook_id))

    # 2. Recursively explore section groups
    def collect_from_group(group_id):
        sections = get_sections_in_group(access_token, site_id, group_id)
        all_sections.extend(sections)
        nested_groups = get_nested_groups(access_token, site_id, group_id)
        for nested_group in nested_groups:
            collect_from_group(nested_group["id"])

    top_level_groups = get_section_groups(access_token, site_id, notebook_id)
    for group in top_level_groups:
        collect_from_group(group["id"])

    return all_sections

# === PAGE RETRIEVAL ===
def get_pages_in_section(access_token, site_id, section_id, timestamp):
    url = (
        f"https://graph.microsoft.com/v1.0/sites/{site_id}/onenote/sections/{section_id}/pages"
        f"?$filter=lastModifiedDateTime ge {timestamp}"
    )
    headers = {"Authorization": f"Bearer {access_token}"}
    response = requests.get(url, headers=headers)
    response.raise_for_status()
    return response.json().get("value", [])

# === MAIN EXECUTION ===
def main():
    access_token = get_token()
    timestamp = get_cst_midnight_iso(2)

    print(f"📅 Checking for changes since: {timestamp}")
    sections = collect_all_sections(access_token, site_id, notebook_id)

    all_updated_pages = []

    for section in sections:
        section_id = section["id"]
        section_name = section["displayName"]
        print(f"\n🔍 Checking section: {section_name}")

        try:
            pages = get_pages_in_section(access_token, site_id, section_id, timestamp)
            if pages:
                print(f"✅ {len(pages)} updated page(s) in '{section_name}'")
                all_updated_pages.extend(pages)
            else:
                print("No changes in this section.")
        except requests.HTTPError as e:
            print(f"⚠️ Error fetching pages for '{section_name}': {e.response.status_code}")
            print(e.response.text)

    if not all_updated_pages:
        print("\n✅ No changes found in the notebook.")
    else:
        print(f"\n📄 Total updated pages: {len(all_updated_pages)}")
        for page in all_updated_pages:
            print(f"- {page['title']} | Last modified: {page['lastModifiedDateTime']}")
            print(f"  URL: {page['links']['oneNoteWebUrl']['href']}")
            print()

if __name__ == "__main__":
    main()