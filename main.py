from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from openai import OpenAI
import faiss
import numpy as np
import json
import os

# === CONFIG ===
EMBEDDING_MODEL = "text-embedding-3-small"
INDEX_FILE = "faiss.index"
METADATA_FILE = "faiss_metadata.json"
CHUNKS_FILE = "onenote_chunks.json"

# === REQUEST BODY MODEL ===
class SearchRequest(BaseModel):
    query: str
    top_k: int = 5
    generate_answer: bool = True  # ✅ allow answer generation toggle

# === INIT OPENAI & LOAD DATA ===
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

index = faiss.read_index(INDEX_FILE)

with open(METADATA_FILE, "r", encoding="utf-8") as f:
    metadata = json.load(f)

with open(CHUNKS_FILE, "r", encoding="utf-8") as f:
    full_chunks = json.load(f)

# === FASTAPI SETUP ===
app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def root():
    return {"status": "ok"}

@app.post("/search")
def search(req: SearchRequest):
    # Embed the query
    response = client.embeddings.create(
        input=req.query,
        model=EMBEDDING_MODEL
    )
    query_vector = np.array([response.data[0].embedding], dtype="float32")

    # Perform FAISS search
    D, I = index.search(query_vector, req.top_k)

    results = []
    context_chunks = []

    for idx in I[0]:
        if idx < len(full_chunks):
            chunk = full_chunks[idx]
            result = {
                "notebook": chunk["metadata"]["notebook"],
                "section": chunk["metadata"]["section"],
                "page": chunk["metadata"]["page"],
                "chunk_index": chunk["metadata"]["chunk_index"],
                "page_url": chunk["metadata"].get("page_url"),
                "text": chunk["text"]
            }
            context_chunks.append(chunk["text"])
            results.append(result)

    # === Optional: Generate GPT answer ===
    final_answer = None
    if req.generate_answer:
        joined_context = "\n\n".join(context_chunks)
        chat = client.chat.completions.create(
            model="gpt-4",
            messages=[
                {
                    "role": "system",
                    "content": "You are a helpful assistant. Only use the provided context to answer. Do not add external knowledge."
                },
                {
                    "role": "user",
                    "content": f"Context:\n\n{joined_context}\n\nAnswer this question:\n{req.query}"
                }
            ]
        )
        final_answer = chat.choices[0].message.content

    return {
        "query": req.query,
        "results": results,
        "answer": final_answer
    }

# Optional: run locally
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)