#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to upload MSAL cache files to S3
Run this script locally to upload your authenticated MSAL cache to S3
"""

import os
import shutil
from dotenv import load_dotenv
from s3_manager import upload_msal_cache_files

def upload_local_cache_to_s3():
    """Upload local MSAL cache files to S3"""
    print("🚀 Uploading local MSAL cache files to S3...")
    
    # Load environment variables
    load_dotenv()
    
    # Check if S3 bucket is configured
    if not os.getenv("S3_BUCKET_NAME"):
        print("❌ S3_BUCKET_NAME not found in environment variables")
        print("   Please set this in your .env file or environment")
        return False
    
    # Check if local cache files exist
    local_files = ["msal_cache.json", "msal_token_cache.bin"]
    missing_files = []
    
    for filename in local_files:
        if not os.path.exists(filename):
            missing_files.append(filename)
    
    if missing_files:
        print(f"❌ Missing local cache files: {missing_files}")
        print("   Please run authentication locally first to generate cache files")
        return False
    
    # Copy local files to /tmp directory (simulating Lambda environment)
    temp_files = []
    try:
        print("📋 Copying local cache files to /tmp...")
        for filename in local_files:
            temp_path = f"/tmp/{filename}"
            shutil.copy2(filename, temp_path)
            temp_files.append(temp_path)
            print(f"   ✅ Copied {filename} to {temp_path}")
        
        # Upload to S3
        print("\n☁️ Uploading to S3...")
        results = upload_msal_cache_files()
        
        # Check results
        all_success = all(results.values())
        if all_success:
            print("\n🎉 Successfully uploaded all MSAL cache files to S3!")
            print("   Your Lambda function can now use these for authentication")
            return True
        else:
            print("\n⚠️ Some uploads failed:")
            for filename, success in results.items():
                status = "✅" if success else "❌"
                print(f"   {status} {filename}")
            return False
    
    except Exception as e:
        print(f"\n❌ Error during upload: {e}")
        return False
    
    finally:
        # Clean up temp files
        print("\n🧹 Cleaning up temporary files...")
        for temp_path in temp_files:
            try:
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                    print(f"   🗑️ Removed {temp_path}")
            except Exception as e:
                print(f"   ⚠️ Could not remove {temp_path}: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("🔐 MSAL Cache Upload to S3")
    print("=" * 60)
    
    success = upload_local_cache_to_s3()
    
    if success:
        print("\n✅ Upload completed successfully!")
        print("💡 Your Lambda function will now download and use these cache files")
    else:
        print("\n❌ Upload failed!")
        print("💡 Please check the errors above and try again")
    
    print("\n" + "=" * 60) 