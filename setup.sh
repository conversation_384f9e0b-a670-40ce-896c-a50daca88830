#!/bin/bash

# =============================================================================
# Knowledge Exchange Bot - Setup Script
# =============================================================================
# This script helps you set up the Knowledge Exchange Bot project
# =============================================================================

set -e  # Exit on any error

echo "🚀 Knowledge Exchange Bot - Setup Script"
echo "=========================================="

# Check if example.env exists
if [ ! -f "example.env" ]; then
    echo "❌ Error: example.env file not found!"
    exit 1
fi

# Step 1: Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "📝 Creating .env file from example.env..."
    cp example.env .env
    echo "✅ .env file created! Please edit it with your actual values."
else
    echo "⚠️  .env file already exists. Skipping creation."
fi

# Step 2: Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Error: Python 3 is not installed!"
    echo "Please install Python 3.8 or higher and try again."
    exit 1
fi

# Step 3: Check if Node.js is installed (for CDK)
if ! command -v node &> /dev/null; then
    echo "❌ Error: Node.js is not installed!"
    echo "Please install Node.js 14+ for AWS CDK and try again."
    exit 1
fi

# Step 4: Create Python virtual environment
if [ ! -d "venv" ]; then
    echo "🐍 Creating Python virtual environment..."
    python3 -m venv venv
    echo "✅ Virtual environment created!"
else
    echo "⚠️  Virtual environment already exists."
fi

# Step 5: Activate virtual environment and install Python dependencies
echo "📦 Installing Python dependencies..."
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
echo "✅ Python dependencies installed!"

# Step 6: Install Node.js dependencies (for CDK)
echo "📦 Installing Node.js dependencies..."
npm install
echo "✅ Node.js dependencies installed!"

# Step 7: Build CDK project
echo "🔨 Building CDK project..."
npm run build
echo "✅ CDK project built successfully!"

# Step 8: Check if AWS CLI is configured
if ! command -v aws &> /dev/null; then
    echo "⚠️  AWS CLI is not installed. You'll need it for deployment."
    echo "Install it from: https://aws.amazon.com/cli/"
else
    echo "✅ AWS CLI is available."
fi

# Step 9: Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "⚠️  Docker is not installed. You'll need it for local testing."
    echo "Install it from: https://docker.com/"
else
    echo "✅ Docker is available."
fi

echo ""
echo "🎉 Setup Complete!"
echo "=================="
echo ""
echo "Next steps:"
echo "1. Edit .env file with your actual API keys and configuration"
echo "2. Test the configuration: python test_docker_lambda.py"
echo "3. Deploy to AWS: npm run deploy"
echo ""
echo "📚 Documentation:"
echo "- README.md - Complete project documentation"
echo "- docs/ - Detailed experience reports"
echo ""
echo "🔗 Useful commands:"
echo "- Activate virtual environment: source venv/bin/activate"
echo "- Run local test: python test_docker_lambda.py"
echo "- Deploy to AWS: cdk deploy"
echo "- View logs: aws logs tail /aws/lambda/function-name --follow"
echo ""
echo "Happy coding! 🚀" 