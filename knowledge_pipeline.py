import os
import json
from datetime import datetime
from typing import List, Dict, Optional
import dotenv
import boto3

from change_detector import ChangeDetector
from content_extractor import ContentExtractor
from embedding_generator import EmbeddingGenerator
from s3_manager import upload_artifacts, update_latest_json, download_latest_files, upload_chunks_file

# SNS Configuration
SNS_TOPIC_ARN = 'arn:aws:sns:us-east-1:203918886422:embedding-updates'

class KnowledgePipeline:
    def __init__(self, openai_api_key: Optional[str] = None):
        self.change_detector = ChangeDetector()
        self.content_extractor = ContentExtractor()
        self.embedding_generator = EmbeddingGenerator(openai_api_key)
        
    def run_full_pipeline(self, days_ago: int = 1) -> Dict:
        """Run the complete knowledge pipeline with proper update handling"""
        print("🚀 Starting Knowledge Pipeline...")
        print("=" * 50)
        
        try:
            # Step 0: Download existing files from S3
            print("\n📋 Step 0: Downloading existing files from S3...")
            existing_files = download_latest_files()
            
            # Step 1: Detect changes
            print("\n📋 Step 1: Detecting changes in OneNote...")
            updated_pages = self.change_detector.detect_changes(days_ago)
            
            if not updated_pages:
                print("✅ No changes detected. Pipeline complete.")
                return {
                    "status": "success",
                    "message": "No changes detected",
                    "updated_pages": 0,
                    "new_chunks": 0
                }
            
            print(f"📄 Found {len(updated_pages)} updated pages")
            
            # Step 2: Extract content from updated pages
            print("\n📋 Step 2: Extracting content from updated pages...")
            new_chunks = self.content_extractor.extract_selective_content(updated_pages)
            
            if not new_chunks:
                print("⚠️ No content extracted from updated pages")
                return {
                    "status": "warning",
                    "message": "No content extracted from updated pages",
                    "updated_pages": len(updated_pages),
                    "updated_pages_details": updated_pages,
                    "new_chunks": 0
                }
            
            print(f"📦 Extracted {len(new_chunks)} chunks from updated pages")
            
            # Step 3: Smart merge with existing chunks (REPLACEMENT LOGIC)
            print("\n📋 Step 3: Merging chunks with replacement logic...")
            
            # Load existing chunks
            existing_chunks = []
            chunks_file = "/tmp/onenote_chunks.json"
            
            if existing_files and existing_files.get("chunks_downloaded", False):
                if os.path.exists(chunks_file):
                    with open(chunks_file, "r", encoding="utf-8") as f:
                        existing_chunks = json.load(f)
                    print(f"📂 Loaded {len(existing_chunks)} existing chunks from S3")
                else:
                    print("⚠️ Chunks file not found locally despite being downloaded")
            else:
                print("📂 No existing chunks file found in S3, starting fresh")
            
            # Create a set of updated page IDs for efficient lookup
            updated_page_ids = set()
            for page in updated_pages:
                page_id = page.get('id') or page.get('page_id')
                if page_id:
                    updated_page_ids.add(page_id)
            
            print(f"🔄 Will replace chunks for {len(updated_page_ids)} updated pages")
            
            # Filter out chunks from pages that were updated (REPLACEMENT)
            filtered_chunks = []
            removed_count = 0
            
            for chunk in existing_chunks:
                chunk_page_id = chunk.get("metadata", {}).get("page_id")
                if chunk_page_id not in updated_page_ids:
                    filtered_chunks.append(chunk)
                else:
                    removed_count += 1
            
            print(f"🗑️ Removed {removed_count} stale chunks from updated pages")
            
            # Add new chunks (APPEND for new pages, REPLACE for updated pages)
            all_chunks = filtered_chunks + new_chunks
            print(f"📦 Total chunks after merge: {len(all_chunks)}")
            
            # Save the updated chunks file
            with open(chunks_file, "w", encoding="utf-8") as f:
                json.dump(all_chunks, f, indent=2, ensure_ascii=False)
            print("💾 Updated onenote_chunks.json with latest content")
            
            # Rebuild the entire index from scratch
            print("\n📋 Step 4: Rebuilding FAISS index...")
            self.embedding_generator.create_new_index(all_chunks)
            self.embedding_generator.save_index()
            
            # Get final index info
            index_info = self.embedding_generator.get_index_info()
            
            # Step 5: Upload to S3
            print("\n☁️ Step 5: Uploading artifacts to S3...")
            s3_result = upload_artifacts("/tmp/faiss.index", "/tmp/faiss_metadata.json")
            chunks_s3_key = upload_chunks_file(chunks_file)
            update_latest_json(s3_result["index_s3_key"], s3_result["metadata_s3_key"], chunks_s3_key)
            
            print("\n✅ Pipeline completed successfully!")
            print(f"📊 Final index: {index_info['total_vectors']} vectors, {index_info['metadata_count']} metadata entries")
            print(f"🔄 Replaced chunks for {len(updated_page_ids)} updated pages")
            print("✨ No duplicate content - clean vector store!")
            
            return {
                "status": "success",
                "message": "Pipeline completed successfully with proper update handling",
                "updated_pages": len(updated_pages),
                "updated_pages_details": updated_pages,
                "new_chunks": len(new_chunks),
                "total_chunks": len(all_chunks),
                "replaced_pages": len(updated_page_ids),
                "removed_stale_chunks": removed_count,
                "index_info": index_info,
                "s3": s3_result
            }
            
        except Exception as e:
            print(f"\n❌ Pipeline failed: {e}")
            return {
                "status": "error",
                "message": str(e),
                "updated_pages": 0,
                "new_chunks": 0
            }
        finally:
            # Always finalize authentication (upload cache to S3)
            print("\n🔄 Finalizing authentication...")
            try:
                self.content_extractor.finalize()
                self.change_detector.finalize()
            except Exception as e:
                print(f"⚠️ Warning: Failed to finalize authentication: {e}")
    
    def run_initial_setup(self) -> Dict:
        """Run initial setup to create full index from all content"""
        print("🚀 Starting Initial Knowledge Setup...")
        print("=" * 50)
        
        try:
            # Step 1: Extract all content
            print("\n📋 Step 1: Extracting all content from OneNote...")
            all_chunks = self.content_extractor.extract_all_content()
            
            if not all_chunks:
                print("❌ No content extracted")
                return {
                    "status": "error",
                    "message": "No content extracted from OneNote",
                    "total_chunks": 0
                }
            
            print(f"📦 Extracted {len(all_chunks)} chunks from all content")
            
            # Step 2: Generate embeddings and create index
            print("\n📋 Step 2: Generating embeddings and creating index...")
            self.embedding_generator.create_new_index(all_chunks)
            self.embedding_generator.save_index()
            
            # Get final index info
            index_info = self.embedding_generator.get_index_info()
            
            # Step 3: Upload to S3
            print("\n☁️ Step 3: Uploading artifacts to S3...")
            s3_result = upload_artifacts("/tmp/faiss.index", "/tmp/faiss_metadata.json")
            
            # Save and upload chunks file
            chunks_file = "/tmp/onenote_chunks.json"
            with open(chunks_file, "w", encoding="utf-8") as f:
                json.dump(all_chunks, f, indent=2, ensure_ascii=False)
            chunks_s3_key = upload_chunks_file(chunks_file)
            
            update_latest_json(s3_result["index_s3_key"], s3_result["metadata_s3_key"], chunks_s3_key)
            
            print("\n✅ Initial setup completed successfully!")
            print(f"📊 Final index: {index_info['total_vectors']} vectors, {index_info['metadata_count']} metadata entries")
            print("📦 Chunks file uploaded to S3")
            
            return {
                "status": "success",
                "message": "Initial setup completed successfully",
                "total_chunks": len(all_chunks),
                "index_info": index_info,
                "s3": s3_result
            }
            
        except Exception as e:
            print(f"\n❌ Initial setup failed: {e}")
            return {
                "status": "error",
                "message": str(e),
                "total_chunks": 0
            }
        finally:
            # Always finalize authentication (upload cache to S3)
            print("\n🔄 Finalizing authentication...")
            try:
                self.content_extractor.finalize()
            except Exception as e:
                print(f"⚠️ Warning: Failed to finalize authentication: {e}")
    
    def test_components(self) -> Dict:
        """Test individual components"""
        print("🧪 Testing Pipeline Components...")
        print("=" * 50)
        
        results = {}
        
        # Test change detector
        try:
            print("\n📋 Testing Change Detector...")
            self.change_detector.authenticate()
            print("✅ Change detector authentication successful")
            results["change_detector"] = "success"
        except Exception as e:
            print(f"❌ Change detector failed: {e}")
            results["change_detector"] = f"error: {e}"
        
        # Test content extractor
        try:
            print("\n📋 Testing Content Extractor...")
            self.content_extractor.authenticate()
            print("✅ Content extractor authentication successful")
            results["content_extractor"] = "success"
        except Exception as e:
            print(f"❌ Content extractor failed: {e}")
            results["content_extractor"] = f"error: {e}"
        
        # Test embedding generator
        try:
            print("\n📋 Testing Embedding Generator...")
            # Just test if we can create the client
            test_info = self.embedding_generator.get_index_info()
            print("✅ Embedding generator initialization successful")
            results["embedding_generator"] = "success"
        except Exception as e:
            print(f"❌ Embedding generator failed: {e}")
            results["embedding_generator"] = f"error: {e}"
        
        return results

def send_embedding_update_notification(result: Dict) -> bool:
    """
    Send SNS notification when embeddings are updated
    Returns True if notification was sent successfully, False otherwise
    """
    try:
        sns = boto3.client('sns')
        
        # Create detailed message
        updated_pages = result.get('updated_pages', 0)
        total_chunks = result.get('total_chunks', 0)
        new_chunks = result.get('new_chunks', 0)
        updated_pages_details = result.get('updated_pages_details', [])
        
        # Build page details section
        page_details = ""
        if updated_pages_details:
            page_details = "\n\n📄 Updated Pages:\n"
            for page in updated_pages_details:
                page_title = page.get('title', 'Unknown')
                section_name = page.get('section_name', 'Unknown Section')
                last_modified = page.get('lastModifiedDateTime', 'Unknown')
                page_url = page.get('links', {}).get('oneNoteWebUrl', {}).get('href', '')
                
                page_details += f"• {page_title} (Section: {section_name})\n"
                page_details += f"  Last modified: {last_modified}\n"
                if page_url:
                    page_details += f"  URL: {page_url}\n"
                page_details += "\n"
        
        message = f"""
🔄 Knowledge Base Embeddings Updated!

📊 Summary:
• Updated Pages: {updated_pages}
• New Chunks: {new_chunks}
• Total Chunks: {total_chunks}
• Status: {result.get('status', 'unknown')}{page_details}
The latest embeddings have been generated and uploaded to the S3 bucket.
Your query system will now have access to the most recent content.

Timestamp: {datetime.now().isoformat()}
        """.strip()
        
        response = sns.publish(
            TopicArn=SNS_TOPIC_ARN,
            Subject='📚 Knowledge Base Embeddings Updated',
            Message=message
        )
        
        print(f"✅ SNS notification sent successfully. MessageId: {response.get('MessageId')}")
        return True
        
    except Exception as e:
        print(f"⚠️ Failed to send SNS notification: {e}")
        return False

# === MAIN EXECUTION ===
if __name__ == "__main__":
    # Get OpenAI API key from environment
    dotenv.load_dotenv()     
    openai_api_key = os.getenv("OPENAI_API_KEY")
    
    if not openai_api_key:
        print("⚠️ Warning: OPENAI_API_KEY not found in environment variables")
        print("   The embedding generation will fail without a valid API key")
    
    pipeline = KnowledgePipeline(openai_api_key)
    
    # Test components first
    print("🧪 Testing components...")
    test_results = pipeline.test_components()
    
    # Check if all components are working
    all_success = all("success" in result for result in test_results.values())
    
    if all_success:
        print("\n✅ All components working! Running pipeline...")
        
        # Check if we have existing index
        if os.path.exists("/tmp/faiss.index") and os.path.exists("/tmp/faiss_metadata.json"):
            print("📂 Found existing index, running incremental update...")
            result = pipeline.run_full_pipeline(days_ago=1)
        else:
            print("📂 No existing index found, running initial setup...")
            result = pipeline.run_initial_setup()
        
        print(f"\n📊 Pipeline Result: {result}")
    else:
        print("\n❌ Some components failed. Please fix the issues above.")
        print("Test results:", test_results)

# === LAMBDA HANDLER ===
def lambda_handler(event, context):
    """
    AWS Lambda handler function
    """
    print("🚀 Lambda function invoked")
    print(f"Event: {event}")
    print(f"Context: {context}")

    try:
        # Load environment variables
        dotenv.load_dotenv()

        # Get OpenAI API key from environment
        openai_api_key = os.getenv("OPENAI_API_KEY")

        if not openai_api_key:
            return {
                'statusCode': 500,
                'body': json.dumps({
                    'error': 'OPENAI_API_KEY not found in environment variables'
                })
            }

        pipeline = KnowledgePipeline(openai_api_key)
        
        # Get days_ago from event or use default
        days_ago = event.get('days_ago', 1) if event else 1
        
        # ALWAYS try to download from S3 first
        print("📂 Attempting to download existing files from S3...")
        existing_files = download_latest_files()
        
        if existing_files and existing_files.get("chunks_downloaded", False):
            print("📂 Successfully downloaded files from S3, running incremental update...")
            result = pipeline.run_full_pipeline(days_ago=days_ago)
        else:
            print("📂 No existing files found in S3, running initial setup...")
            result = pipeline.run_initial_setup()
        
        print(f"📊 Pipeline Result: {result}")
        
        # Check if embeddings were updated and send notification
        if result.get('status') == 'success':
            updated_pages = result.get('updated_pages', 0)
            
            if updated_pages > 0:
                # Embeddings were updated, send notification
                print("📧 Embeddings were updated, sending SNS notification...")
                notification_sent = send_embedding_update_notification(result)
                result['notification_sent'] = notification_sent
            else:
                # No updates detected
                print("📧 No updates detected. No SNS notification sent.")
                result['notification_sent'] = False
                result['notification_reason'] = 'No updates detected'
        else:
            # Pipeline failed, don't send notification
            print("📧 Pipeline failed. No SNS notification sent.")
            result['notification_sent'] = False
            result['notification_reason'] = f"Pipeline failed: {result.get('message', 'Unknown error')}"
        
        return {
            'statusCode': 200,
            'body': json.dumps(result)
        }
        
    except Exception as e:
        print(f"❌ Lambda function failed: {e}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'error': str(e)
            })
        } 