# Git
.git
.gitignore

# Node.js
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log

# CDK
cdk.out
*.js
*.d.ts
tsconfig.json
cdk.json
package.json
package-lock.json

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Data files (but allow Microsoft auth cache)
faiss.index
faiss_metadata.json
onenote_chunks.json
token_cache.bin

# IDE
.vscode
.idea
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Documentation
README.md
*.md

# Test files
test/
tests/
*_test.py
test_*.py 