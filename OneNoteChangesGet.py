import os
import requests
import msal

client_id = "073b50d3-d544-4140-816e-367dbe6c2641"
tenant_id = "25c282df-1bd0-4ae9-915e-ea3003bdd835"
authority = f"https://login.microsoftonline.com/{tenant_id}"
scope = ["Notes.Read.All", "Sites.Read.All"]

# Token cache location
TOKEN_CACHE_FILE = "msal_token_cache.bin"

# Create a token cache object and load if it exists
cache = msal.SerializableTokenCache()
if os.path.exists(TOKEN_CACHE_FILE):
    cache.deserialize(open(TOKEN_CACHE_FILE, "r").read())

# Create a public client app with cache
app = msal.PublicClientApplication(client_id, authority=authority, token_cache=cache)

# Check for existing account/token
accounts = app.get_accounts()
if accounts:
    result = app.acquire_token_silent(scope, account=accounts[0])
else:
    flow = app.initiate_device_flow(scopes=scope)
    if "user_code" not in flow:
        raise ValueError("Failed to initiate device flow")
    print(flow["message"])
    result = app.acquire_token_by_device_flow(flow)

# Save the cache back to disk if it changed
if cache.has_state_changed:
    with open(TOKEN_CACHE_FILE, "w") as f:
        f.write(cache.serialize())

# Proceed with call to Graph
if "access_token" in result:
    access_token = result["access_token"]
    headers = {"Authorization": f"Bearer {access_token}"}

    SITE_ID = 'creospaninc.sharepoint.com,e403106b-31d0-4181-b487-2ef10c039907,c3316d29-5ff0-4cbb-965a-a57f407cd771'
    url = f"https://graph.microsoft.com/v1.0/sites/{SITE_ID}/onenote/notebooks"
    response = requests.get(url, headers=headers)

    print("Status:", response.status_code)
    if response.ok:
        for notebook in response.json().get("value", []):
            print(f"Notebook: {notebook['displayName']} | ID: {notebook['id']}")
    else:
        print("Error:", response.text)
else:
    print("Auth failed:", result.get("error_description"))